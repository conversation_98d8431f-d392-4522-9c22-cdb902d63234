const { Pool } = require('pg');

/**
 * Database functions for saved_views table
 * Handles user's custom table views (sort, grouping, filters)
 */

/**
 * Get all saved views for a user and table
 * @param {Object} pool - PostgreSQL connection pool
 * @param {string} userName - User name
 * @param {string} tableName - Table name (e.g., "PE Request Table")
 * @returns {Array} Array of saved views
 */
async function getSavedViews(pool, userName, tableName) {
    const query = `
        SELECT id, view_name, sort_state, grouping_state, filters_state, created_at, updated_at
        FROM saved_views 
        WHERE user_name = $1 AND table_name = $2
        ORDER BY created_at DESC
    `;
    
    try {
        const result = await pool.query(query, [userName, tableName]);
        return result.rows;
    } catch (error) {
        console.error('Error getting saved views:', error);
        throw error;
    }
}

/**
 * Create a new saved view
 * @param {Object} pool - PostgreSQL connection pool
 * @param {Object} viewData - View data object
 * @param {string} viewData.userName - User name
 * @param {string} viewData.tableName - Table name
 * @param {string} viewData.viewName - View name
 * @param {Object} viewData.sortState - Sort state object
 * @param {string} viewData.groupingState - Grouping state string
 * @param {Object} viewData.filtersState - Filters state object
 * @returns {Object} Created view with ID
 */
async function createSavedView(pool, viewData) {
    const { userName, tableName, viewName, sortState, groupingState, filtersState } = viewData;
    
    const query = `
        INSERT INTO saved_views (user_name, table_name, view_name, sort_state, grouping_state, filters_state)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, view_name, sort_state, grouping_state, filters_state, created_at, updated_at
    `;
    
    try {
        const result = await pool.query(query, [
            userName, 
            tableName, 
            viewName, 
            JSON.stringify(sortState), 
            groupingState, 
            JSON.stringify(filtersState)
        ]);
        return result.rows[0];
    } catch (error) {
        if (error.code === '23505') { // Unique constraint violation
            throw new Error(`A view named "${viewName}" already exists for this table`);
        }
        console.error('Error creating saved view:', error);
        throw error;
    }
}

/**
 * Update an existing saved view
 * @param {Object} pool - PostgreSQL connection pool
 * @param {number} viewId - View ID
 * @param {string} userName - User name (for security)
 * @param {Object} updateData - Data to update
 * @returns {Object} Updated view
 */
async function updateSavedView(pool, viewId, userName, updateData) {
    const { viewName, sortState, groupingState, filtersState } = updateData;
    
    const query = `
        UPDATE saved_views 
        SET view_name = $1, sort_state = $2, grouping_state = $3, filters_state = $4, updated_at = CURRENT_TIMESTAMP
        WHERE id = $5 AND user_name = $6
        RETURNING id, view_name, sort_state, grouping_state, filters_state, created_at, updated_at
    `;
    
    try {
        const result = await pool.query(query, [
            viewName,
            JSON.stringify(sortState),
            groupingState,
            JSON.stringify(filtersState),
            viewId,
            userName
        ]);
        
        if (result.rows.length === 0) {
            throw new Error('View not found or access denied');
        }
        
        return result.rows[0];
    } catch (error) {
        console.error('Error updating saved view:', error);
        throw error;
    }
}

/**
 * Delete a saved view
 * @param {Object} pool - PostgreSQL connection pool
 * @param {number} viewId - View ID
 * @param {string} userName - User name (for security)
 * @returns {boolean} Success status
 */
async function deleteSavedView(pool, viewId, userName) {
    const query = `
        DELETE FROM saved_views 
        WHERE id = $1 AND user_name = $2
    `;
    
    try {
        const result = await pool.query(query, [viewId, userName]);
        return result.rowCount > 0;
    } catch (error) {
        console.error('Error deleting saved view:', error);
        throw error;
    }
}

/**
 * Get a specific saved view by ID
 * @param {Object} pool - PostgreSQL connection pool
 * @param {number} viewId - View ID
 * @param {string} userName - User name (for security)
 * @returns {Object} View data
 */
async function getSavedViewById(pool, viewId, userName) {
    const query = `
        SELECT id, view_name, sort_state, grouping_state, filters_state, created_at, updated_at
        FROM saved_views 
        WHERE id = $1 AND user_name = $2
    `;
    
    try {
        const result = await pool.query(query, [viewId, userName]);
        if (result.rows.length === 0) {
            throw new Error('View not found or access denied');
        }
        return result.rows[0];
    } catch (error) {
        console.error('Error getting saved view by ID:', error);
        throw error;
    }
}

module.exports = {
    getSavedViews,
    createSavedView,
    updateSavedView,
    deleteSavedView,
    getSavedViewById
};
