
//require("dotenv").config();
import 'dotenv/config'
import config from './config.js';
//import config from './config_olddb.js';

//const getByJobID = require('./dbfunctions/mfgtagsbyJobIDdb') //query for getting all tags by jobID
import getMfgTagsByJobID from './dbfunctions/mfgtagsbyJobIDdb.js'

import getMfgTagByTagID from './dbfunctions/mfgtagbyTagIDdb.js' //query to get tag by tagID
import getNextAvailTag from'./dbfunctions/mfgtagGetNextTag.js' //query to get the next available tagID (run every time [ even in batch - might be some orphaned tagID ] )
import updateStatusTag from './dbfunctions/mfgtagUpdateStatus.js'
import createNewTag from './dbfunctions/mfgtagCreateTag.js'
import addNewDept from './dbfunctions/mfgtagaddnewdept.js'
import updateDeptStatus from './dbfunctions/mfgtagUpdateDeptStatus.js'
import updateTag from './dbfunctions/mfgtagUpdateTag.js'
import getMostRecentTags from './dbfunctions/mfgtagGetMostRecent.js' // tags: get latest tags
import getLatestJobs from './dbfunctions/mfgtagLatestJobs.js' // tags: get latest jobs
import getMaxChangeOrder from './dbfunctions/mfgtagMaxChangeOrder.js' // tags: get max changeorder (for create tag)
import getAllDeptDevices from './dbfunctions/mfgtagGetAllDeptDevices.js' // tags: get all depts and devices
import getJobInfoNotag from './dbfunctions/mfgtagGetJobInfo.js' // get job info when no tags are there
import totTags from './dbfunctions/mfgtagGetTotTags.js' // get total tags so we know what number the next one will have. 
import getDeviceStatus from './dbfunctions/mfgtagDeviceStatus.js';

import getAllDepts from './dbfunctions/depts_all_depts.js' //deptslive
import generateQRCodeAll from './dbfunctions/mfgtagGenerateQRCodeAll.js'  //require('./dbfunctions/mfgtagGenerateQRCodeAll') 
import generateQRCodePKAll from './dbfunctions/mfgreqPKGenerateQRCodeAll.js'  //require('./dbfunctions/mfgtagGenerateQRCodeAll') 
import generateQRCodePK from './dbfunctions/mfgreqPKGenerateQRCode.js'

import getChangeOrderForJobNumber from './dbfunctions/mfgreqGetChangeOrders.js'
import getChangeOrderForJobNumberForPackout from './dbfunctions/mfgreqGetChangeOrdersForPackout.js'
import createNewReq from './dbfunctions/mfgreqCreateNewReq.js'
import createPKReq from './dbfunctions/mfgreqPKCreateNewReq.js'
import updateRequest from './dbfunctions/mfgreqUpdateRequest.js'
import updatePKReq from './dbfunctions/mfgreqPKUpdateReq.js'
import getAllRequests from './dbfunctions/mfgreqGetAllRequests.js'

import getSearchRequests from './dbfunctions/mfgreqSearchRequestv2.js' //mssql version
import getMFGRequests from './dbfunctions/pg/mfg_reqs_search.js'; //pg version
import getSingleMFGRequest from './dbfunctions/pg/mfg_req_get_single_req.js';
import getSinglePKRequest from './dbfunctions/pg/pk_req_get_single_req.js';
import getSingleSHRequest from './dbfunctions/pg/sh_req_get_single_req.js';
import updateNotifAsRead from './dbfunctions/pg/comm_mark_notif_as_read.js';
import updateNotifAsNotRead from './dbfunctions/pg/comm_mark_notif_as_not_read.js';


import getMFGRequestStandAlone from './dbfunctions/mfgreqGetMFGRequestsStandAlone.js'
import getTagsForMFGRequest from './dbfunctions/mfgreqgetTagsForMFGrequest.js'

import getSearchPackoutRequests from './dbfunctions/mfgreqSearchPackoutRequest.js' //mssql version
import getPKRequests from './dbfunctions/pg/pk_reqs_search.js'; //pg version


import getSearchShippingRequests from './dbfunctions/mfgreqSearchShippingRequest.js' //mssql version
import getSHRequests from './dbfunctions/pg/sh_req_search.js'; //pg version

import getPRJMGNTRequests from './dbfunctions/pg/prj_mgnt_search.js';
import getPRJMGNTSephoraRow from './dbfunctions/pg/prj_mgnt_get_sephora_row.js';
import getAllSephoraMasterStatus from './dbfunctions/pg/prj_mgnt_get_sephora_master_status.js';
import getPRJMGNTFormFieldsOptions from './dbfunctions/pg/prj_mgnt_get_form_field_options.js';
import upsertProject from './dbfunctions/pg/prj_mgnt_upsert.js';


import getPackoutRequestInfo from './dbfunctions/mfgreqPKInfo.js'
import getPackoutFiles from './dbfunctions/mfgreqPKlookupPKInfo.js'
import getPKForJobNumberForShipping from './dbfunctions/mfgreqGetPKForShipping.js';
import createSHReq from './dbfunctions/mfgreqSHCreateNewReq.js'
import updateSHReq from './dbfunctions/mfgreqSHUpdateReq.js'
import updateReqSHstatus from './dbfunctions/mfgreqSHupdateStatus.js';

import getOFRequests from './dbfunctions/pg/of_reqs_search.js'

import getPrintRequests from './dbfunctions/pg/pr_reqs_search.js'

import getPrepRequests from './dbfunctions/pg/pe_reqs_search.js';

import getPlanoRequests from './dbfunctions/pg/pi_reqs_search.js';

import runMergeStoredProc from './dbfunctions/mfgRunMerge.js'

import getFloorUsers from './dbfunctions/mfggeneralGetFloorUsers.js'
import getPMs from './dbfunctions/mfggeneralGetPMs.js'
import getUsersForAutoComplete from './dbfunctions/general_get_users_autocomplete.js'

import getInventoryItems from './dbfunctions/inventory_get_all_items.js' // inventory
import getTotalItems from './dbfunctions/inventory_get_total_items.js' // inventory
import getTotalSearchItems from './dbfunctions/inventory_get_total_search_items.js' // inventory
import getInventorySearchItems from './dbfunctions/inventory_get_search_items.js' // inventory
import getInventoryItemCodes from './dbfunctions/inventory_get_all_item_codes.js' // inventory
import getInventoryNewItemCode from './dbfunctions/inventory_get_new_item_code.js' //inventory
import getInventoryUOMCodes from  './dbfunctions/inventory_get_all_uom.js' //inventory
import getNewItemRequest from './dbfunctions/inventory_create_new_item_request.js'
import createNewItemRequest from './dbfunctions/inventory_get_new_item_request.js' //inventory
import inventoryItemRef from './dbfunctions/inventory_item_ref.js' // hybrid for both tags and inventory
import getTagsSubstratesPerItemCode from './dbfunctions/inventory_get_substrates_for_aliases.js'
import getAllItemsDesc from  './dbfunctions/inventory_get_all_items_desc.js'
import upsertItemAlias from './dbfunctions/inventory_upsert_item_alias.js'
// above: this is actually where the brige is made between tags and inventory. 
// for this endpoint we keep the "/api/mfgtags" and not "/api/inventory"

import getAllDevices from './dbfunctions/devices_get_all_devices.js' // devices: get all devices.
import  getAllDeviceTypes from './dbfunctions/devices_get_all_device_types.js' // devices: get all device types
import getAllDeviceHistory from './dbfunctions/device_get_all_history.js' // devices: get all device history
import getAllDevicesByDept from './dbfunctions/devices_get_all_devices_by_dept.js' // devices: get all devices used in deptslive

import getAllNotifications from './dbfunctions/pg/comm_get_notifications.js' //general get all notifications
import getAllMessages from './dbfunctions/general_get_all_messages_user_sent.js' //general get all notifications
import getAllMessagesByReference from './dbfunctions/general_get_all_message_by_reference.js' // general get all notifications (messages) for one subject.
import getAllMessagesUserIsMentionned from './dbfunctions/general_get_all_messages_user_mentioned.js';
import getAllCommentsBy from './dbfunctions/pg/comm_get_all_by_author.js'
import insertComment from './dbfunctions/pg/comm_insert_comment.js';


import sendMsgReply from './dbfunctions/general_send_reply_message.js' //general send a message reply
import msgMarkAsRead from './dbfunctions/general_msg_mark_as_read.js' // general mark a message as read
//const testEmail = require('./dbfunctions/general_send_email') //testing send email
import sendEmail from './dbfunctions/general_send_email.js'
import upsertUserDbInfo from './dbfunctions/general_upsert_user_usage.js';
import getUserDbInfo from './dbfunctions/general_get_user_usage.js';

import addComment from './dbfunctions/general_add_comment.js'
import getAllComments from './dbfunctions/general_get_all_comments.js'

import getExcludeRebate from './dbfunctions/reports_exclude_rebate.js' // reporting end point
import getAllReports from './dbfunctions/reports_list_all_reports.js'
import getInvoiceDetails from './dbfunctions/reports_get_invoice_details.js';
import getBlueCostDetails from './dbfunctions/reports_get_blue_cost_info.js';
import getAllSalesRep from './dbfunctions/general_get_sales_reps.js';

import getAllEstimates from './dbfunctions/estimates_list_all.js'; //estimates enf point
import createNewEstimate from './dbfunctions/estimates_create.js';
import getAllClient from './dbfunctions/general_get_all_clients.js';

//const updateShipping = require('./dbfunctions/general_update_afterShip') // general shipping updater
import updateShipStatus from './dbfunctions/general_update_afterShipV2.js'
import getShipInfoByJobNumber from './dbfunctions/general_get_ship_info.js'
import getMultiShipByJobNumber from './dbfunctions/general_get_multi_ship_by_job_number.js';
import createPackingSlip from './dbfunctions/general_create_multi_packing_slip.js';
// import searchLdapUsers from './dbfunctions/pg/gen_ad_search.js'
//const dailyShippingReport = require('./dbfunctions/general_send_shipping_report')

import getTokenNoAuth from './dbfunctions/mfggeneralFlatAuth.js' //flat unsecure auth.
import upsertTableSetting from './dbfunctions/pg/gen_upsert_table_setttings.js';
import getTableSettingsByTemplate from './dbfunctions/pg/gen_get_table_settings_by_template.js'
import getTableFields from './dbfunctions/pg/gen_get_table_fields.js'
import getTagsData from './dbfunctions/pg/tags_get_by_jn_and_co.js'
import getTagRequests from './dbfunctions/pg/tag_req_search.js'
import getSephoraImports from './dbfunctions/pg/sephora_imports_search.js';

import upsertSwitch from './dbfunctions/pg/upsert_switch.js';

import getSingleRequestFromSearchIndex from './dbfunctions/pg/gen_get_single_req_by_pk.js'

import getJobAndCOInfoByJn from './dbfunctions/v2/job_and_co_info_by_jn.js'
import getMFGByJn from './dbfunctions/pg/mfg_get_by_jn.js'
import getAuditByID from './dbfunctions/pg/get_audit_by_id.js'
import getTableNames from './dbfunctions/pg/gen_get_table_names.js';
import * as savedViewsDb from '../dbfunctions/pg/saved_views.js';



//const mfgtags = require('./mfgtags') //data models
import swaggerUi from 'swagger-ui-express'
//import swaggerDoc from '../swagger_output.json'

import axios from 'axios';
import session from 'express-session'
import passport from 'passport'
import ADS from 'passport-activedirectory'
import jwt from 'jsonwebtoken'
import express from 'express'
import morgan from 'morgan'
import path from 'path'
import rfs from 'rotating-file-stream'
import https from 'https'
import fs from 'fs'
import bodyParser from 'body-parser'
import cors from 'cors'
import moment from 'moment'
import { CronJob } from 'cron'
import { fileURLToPath } from 'url';

//const logger = require('./logger')
//console.log(logger.logger)

import { request, response }  from 'express';
//const { job } = require("cron");
const app = express()
import promBundle  from 'express-prom-bundle'
import mfgRunMerge from "./dbfunctions/mfgRunMerge.js"
import { get } from 'http';



const metricsMiddleware = promBundle({
    includeMethod: true, 
    includePath: true, 
    includeStatusCode: true, 
    includeUp: true,
    customLabels: {project_name: 'ImtechGraphicsAppsAPI', project_type: 'endpoint_usage_metrics'},
    promClient: {
    collectDefaultMetrics: {
    }
    }
   });

app.use(metricsMiddleware)

//unsecure endpoint!!
app.use('/flatlogin/:username', function(req, res){
    //console.log(req.params)
    res.setHeader('Access-Control-Allow-Origin', '*')
    getTokenNoAuth(req.params.username).then((data) => {
        res.json(data);
    });
})

app.use(session({
    secret: 'keyboard cat',
    resave: true,
    saveUninitialized: true
  }));

const __filename = fileURLToPath(import.meta.url);

const __dirname = path.dirname(__filename);

var accessLogStream = rfs.createStream('access.log',{
    interval: '1d',
    path: path.join(__dirname, 'log')
})

app.use(morgan('combined', { stream: accessLogStream }))
// below is all the code to authenticate to ActiveDirectory. 
passport.use(new ADS({
    integrated: false,
    ldap: {
        url: process.env.AD_SERVER,
        baseDN: 'dc=imtech,dc=lan',
        username: process.env.AD_USER_NAME,
        password: process.env.AD_PASSWORD
    }
}, function(profile, ad, done) {
    ad.isUserMemberOf(profile._json.dn, 'AccessGroup', function(err,isMember){
        if (err) return done(err)
        const displayName = profile._json.displayName
        const token = jwt.sign({displayName}, process.env.JWT_SECRET,{expiresIn: '365d'})
        profile._json['token'] = token;
        return done(null, profile)
    })
}))
passport.serializeUser(function(user, done) {
    done(null, user);
});
  
passport.deserializeUser(function(user, done) {
    done(null, user);
});

app.use(passport.initialize());
app.use(passport.session());
app.use(passport.authenticate('session'))

app.use(bodyParser.urlencoded({extended: true}))
app.use(bodyParser.json())
app.use(cors())



const router = express.Router()
// router.use('/api-docs', swaggerUi.serve)
// router.get('/api-docs', swaggerUi.setup(swaggerDoc))

// Middleware to bypass authentication in development
const bypassAuth = (req, res, next) => {
    if (req.body.username === 'test_itadmin'|| req.body.username === 'gav') {
        console.log("######################## logging in using ByPass #############################################")
      const fakeUser = {
        username: req.body.username,
        role: 'admin', 
      };
        const profile = {}
        profile['_json'] = {}
        profile._json['displayName'] = req.body.username;
        const displayName = req.body.username;
        const token = jwt.sign({displayName}, process.env.JWT_SECRET,{expiresIn: '365d'});
        profile._json['token'] = token;
        console.log(profile)
        return  res.json(profile)
    } else {
        next(); // Continue regular authentification process if it's not this user
    }
};

var opts = { }
app.post('/login', bypassAuth, passport.authenticate('ActiveDirectory', opts), function(req, res){
    res.json(req.user)
}, function(err) {
    res.status(401).send('Not authenticated')
})

/*app.get('/logout', function (req, res){
    req.session.destroy(function (err) {
      res.redirect('/'); 
    });
  });*/

  app.get('/logout', function (req, res) {
    req.logOut();
    res.status(200).clearCookie('connect.sid', {
      path: '/'
    });
    req.session.destroy(function (err) {
      res.redirect('/');
    });
  });

  function isAuthenticated(req, res, next){
    if(req.user)
        return next();
    else
        return res.status(401).json({
            error: 'user not authenticated'
        })    
    }


    function ensureAuthenticated(req, res, next) {

        if (!req.headers.authorization) {
           return res.status(401).send({ message: 'Please make sure your request has an Authorization header' });
        }
        var token = req.headers.authorization.split(' ')[1];
      
        var payload = null;
        try {
          payload = jwt.decode(token, process.env.JWT_SECRET);
        }
        catch (err) {
          return res.status(401).send({ message: err.message });
        }
      
        if (payload.exp <= moment().unix()) {
          return res.status(401).send({ message: 'Token has expired' });
        }
        req.user = payload.sub;
        next();
      }


    app.get('/checkauth', isAuthenticated, function(req, res){
        res.status(200).json({
            status: 'correctly logged in'
        });
    });

    


//!important: the folder structure for Imtech wide API should be as follow:
// "/api/{app_name}" that's for the root. The rest will be the actual pages. 
//example: https://server_name:port_number/api/{app_name}/{function}/{:id}(optional)
app.use('/api/mfgtags', router) 

router.use((request, response, next) => {
    console.log('middleware');
    next();
});

const sendSSE = (data) => {
    res.write(`data: ${JSON.stringify(data)}\n\n`);
  };

//SSE endpoints
app.get('/api/sse/api-status', (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
  
    const sendKeepAlive = () => {
      res.write(`data: {"status": "alive"}\n\n`);
    };
  
    // Send a "status" message every 15 seconds
    const intervalId = setInterval(sendKeepAlive, 15000);
  
    req.on('close', () => {
      clearInterval(intervalId);
      res.end();
    });
  
    sendKeepAlive();
  });




app.get('/api/sse/shipping/:timestamp', async (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    var r = {}
    var key = 'timestamp'
    r[key] = req.params.timestamp
    //console.log(r)
    console.log(`\u001b[1;35m********** Shipping Requests ******************\x1b[0m`)
    
    console.log(`\u001b[1;35mShipping Endpoint: /api/sse/shipping/:timestamp\x1b[0m`)
    console.log(`\u001b[1;35mShipping requests timestamp (req.params.timestamp): ${r.timestamp}\x1b[0m`)
    console.log(`\u001b[1;35mObject passed to Shipping function to run query: ${JSON.stringify(r)}\x1b[0m`)
    getSearchShippingRequests(r).then((data) =>{
        if(data.recordset) {
            console.log(`\u001b[1;35mShipping Data:  ${data.rowsAffected.length} records\x1b[0m`)
            // res.sendStatus(200);
            res.write(`data: ${JSON.stringify(data)}\n\n`);
        } else {
            console.log('\u001b[1;35mShipping Data: NO records\x1b[0m')
            defaultData = `{
                recordsets: [ [ null ] ],
                recordset: [ null ],
                output: {},
                rowsAffected: [ 0 ]
                }
                `
                res.write(`data: ${JSON.stringify(defaultData)}\n\n`);
        }
        console.log(`\u001b[1;35m********** Shipping Requests End ******************\x1b[0m`)
    });
  
    const keepAlive = setInterval(() => {
      res.write(': keep-alive\n\n');
    }, 15000);
  
    // Handle client disconnection
    req.on('close', () => {
      clearInterval(keepAlive);
      res.end();
    });
});


app.get('/api/sse/mfg/:timestamp', async (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    var r = {}
    var key = 'timestamp'
    r[key] = req.params.timestamp
    // console.log(r)
    // console.log(r.timestamp)
    console.log(`\u001b[1;36m********** MFG Requests ******************\x1b[0m`)
    console.log(`\u001b[1;36mMFG Endpoint: /api/sse/mfg/:timestamp\x1b[0m`)
    console.log(`\u001b[1;36mMFG requests timestamp (req.params.timestamp): ${r.timestamp}\x1b[0m`)
    console.log(`\u001b[1;36mObject passed to MFG function to run query: ${JSON.stringify(r)}\x1b[0m`)
    getSearchRequests(r).then((data) =>{
        if(data.recordset) {
            // res.sendStatus(200);
            console.log(`\u001b[1;36mMFG Data:  ${data.rowsAffected.length} records\x1b[0m`)
            res.write(`data: ${JSON.stringify(data)}\n\n`);
        } else {
            console.log('\u001b[1;36mMFG Data: NO records\x1b[0m')
            defaultData = `{
                recordsets: [ [ null ] ],
                recordset: [ null ],
                output: {},
                rowsAffected: [ 0 ]
                }
                `
            res.write(`data: ${JSON.stringify(defaultData)}\n\n`);
        }
        console.log(`\u001b[1;36m********** MFG Requests End ******************\x1b[0m`)
    });
  
    const keepAlive = setInterval(() => {
      res.write(': keep-alive\n\n');
    }, 15000);
  
    // Handle client disconnection
    req.on('close', () => {
      clearInterval(keepAlive);
      res.end();
    });
});


app.get('/api/sse/pk/:timestamp', async (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    var r = {}
    var key = 'timestamp'
    r[key] = req.params.timestamp
    // console.log(r)
    // console.log('*************************************** ',r.timestamp, ' ***************************************')
    console.log(`\u001b[1;32m********** PK Requests ******************\x1b[0m`)
    console.log(`\u001b[1;32mPK Endpoint: /api/sse/pk/:timestamp\x1b[0m`)
    console.log(`\u001b[1;32mPK requests timestamp (req.params.timestamp): ${r.timestamp}\x1b[0m`)
    console.log(`\u001b[1;32mObject passed to PK function to run query: ${JSON.stringify(r)}\x1b[0m`)
    getSearchPackoutRequests(r).then((data) =>{

        console.log('This is data: ', data)
        if(data.recordset) {
            console.log(`\u001b[1;32mPK Data:  ${data.rowsAffected.length} records\x1b[0m`)
            res.write(`data: ${JSON.stringify(data)}\n\n`);
           
        } else {
            console.log(`\u001b[1;42mPK Data:  NO records\x1b[0m`)
            defaultData = `{
                recordsets: [ [ null ] ],
                recordset: [ null ],
                output: {},
                rowsAffected: [ 0 ]
                }
                `
            res.write(`data: ${JSON.stringify(defaultData)}\n\n`);
        }
        console.log(`\u001b[1;32m********** PK Requests End ******************\x1b[0m`)
    });
  
    const keepAlive = setInterval(() => {
      res.write(': keep-alive\n\n');
    }, 15000);
  
    // Handle client disconnection
    req.on('close', () => {
      clearInterval(keepAlive);
      res.end();
    });
});



app.get('/api/sse/tags/:shipdate', async (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    var r = {}
    var key = 'shipdate'
    r[key] = req.params.shipdate
    // console.log("HELLLOOOOOOO: this is from the tags sse")
    // console.log(r)
    // console.log(r.shipdate)
    console.log(`\u001b[1;33m********** Tags Requests ******************\x1b[0m`)
    console.log(`\u001b[1;33mTags Endpoint: /api/sse/pk/:timestamp\x1b[0m`)
    console.log(`\u001b[1;33mTags requests timestamp (req.params.timestamp): ${r.timestamp}\x1b[0m`)
    console.log(`\u001b[1;33mObject passed to Tags function to run query: ${JSON.stringify(r)}\x1b[0m`)
    getTagsForMFGRequest(r).then((data) =>{

        //console.log('This is data from getTagsForMFGRequest: ', data)
        if(data.recordset) {
            // res.sendStatus(200);
            console.log(`\u001b[1;33mTags Data:  ${data.rowsAffected.length} records\x1b[0m`)
            res.write(`data: ${JSON.stringify(data)}\n\n`);
           
        } else {
            console.log(`\u001b[1;33mTags Data:  NO records\x1b[0m`)
            defaultData = `{
                recordsets: [ [ null ] ],
                recordset: [ null ],
                output: {},
                rowsAffected: [ 0 ]
                }
                `
            res.write(`data: ${JSON.stringify(defaultData)}\n\n`);
        }
        console.log(`\u001b[1;33m********** Tags Requests End ******************\x1b[0m`)
    });
  
    const keepAlive = setInterval(() => {
      res.write(': keep-alive\n\n');
    }, 15000);
  
    // Handle client disconnection
    req.on('close', () => {
      clearInterval(keepAlive);
      res.end();
    });
});


app.get('/api/sse/devicestatus/', async (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    console.log(`\u001b[1;37m********** Devices Requests ******************\x1b[0m`)
    console.log(`\u001b[1;37mDevices  Endpoint: /api/sse/pk/:timestamp\x1b[0m`)
    
    
    getDeviceStatus().then((data) =>{

        //console.log('This is data from getDeviceStatus: ', data)
        if(data.recordset) {
            // res.sendStatus(200);
            console.log(`\u001b[1;37mDevices Data:  ${data.rowsAffected.length} records\x1b[0m`)
            res.write(`data: ${JSON.stringify(data)}\n\n`);
           
        } else {
            console.log(`\u001b[1;33mDevices Data:  NO records\x1b[0m`)
            defaultData = `{
                recordsets: [ [ null ] ],
                recordset: [ null ],
                output: {},
                rowsAffected: [ 0 ]
                }
                `
            res.write(`data: ${JSON.stringify(defaultData)}\n\n`);
        }
        console.log(`\u001b[1;37m********** Devices Requests End******************\x1b[0m`)
    });
  
    const keepAlive = setInterval(() => {
      res.write(': keep-alive\n\n');
    }, 15000);
  
    // Handle client disconnection
    req.on('close', () => {
      clearInterval(keepAlive);
      res.end();
    });
});


// app.post('/api/mfgtags/tagsformfgrequests/', ensureAuthenticated, function(request, response){ 
//     getTagsForMFGRequest(request.body).then((data) =>{
//         response.json(data);
//     });
// });




//SSE endpoints until here.


app.get('/api/mfgtags/pdf-proxy/:pkid', ensureAuthenticated, async (req, res) => {
    try {
      const pkid = req.params.pkid; // example param
      const intranetUrl = `http://reportal.intranet.lan/showReportFromTrustedIP.aspx?id=157&P1=${pkid}`;
  
      const response = await axios.get(intranetUrl, { responseType: 'arraybuffer' });
      res.set('Content-Type', 'application/pdf');
      // res.set('Content-Disposition', 'attachment; filename="somefile.pdf"'); // optional
      res.send(response.data);
  
    } catch (error) {
      console.error('Proxy error:', error);
      res.status(500).send('Could not retrieve PDF');
    }
  });


app.get('/api/mfgtags/mfgtagsbyjobid/:id', ensureAuthenticated, function(req, res){
    getMfgTagsByJobID(req.params.id).then((data) => {
    res.json(data);
    })
}, function(err) {
    res.status(401).send('Not authenticated')
})

/*router.route('/mfgtagsbyjobid/:id').get((request, response) => { //query is in ./dbfunctions/mfgtagsbyJobIDdb
    getByJobID.getMfgTagsByJobID(request.params.id).then((data) => {
        response.json(data);
    });
});*/

app.get('/api/mfgtags/mfgtagbytagid/:id', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagbyTagIDdb
    getMfgTagByTagID(request.params.id).then((data) => {
        response.json(data);
    });
});



app.get('/api/mfgtags/totnumoftags/:jobNumber', ensureAuthenticated, function(request, response){ 
    totTags(request.params.jobNumber).then((data) => {
        response.json(data);
    });
});


app.put('/api/mfgtags/mfgtagupdatestatus/', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagUpdateStatus
    //let statusbody = JSON.parse(request.body);
    updateStatusTag(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgtagcreatenewtag/', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagCreateTag
    //let statusbody = JSON.parse(request.body);
    createNewTag(request.body).then((data) => {
        response.json(data);
    });
});

app.put('/api/mfgtags/mfgtagedittag/', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagUpdateTag
    //let statusbody = JSON.parse(request.body);
    updateTag(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgtagaddnewdept/', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagaddnewdept
    //let statusbody = JSON.parse(request.body);
    addNewDept(request.body).then((data) => {
        response.json(data);
    });
});

app.put('/api/mfgtags/mfgtagdeptupdatestatus/', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagUpdateDeptStatus
    //let statusbody = JSON.parse(request.body);
    updateDeptStatus(request.body).then((data) => {
        response.json(data);
    });
});

app.get('/api/mfgtags/mfgtagsgetnextavail/:id', ensureAuthenticated, function(request, response){ //query is in ./dbfunction/mfgtagGetNextTag - takes a JobID to work
    getNextAvailTag(request.params.id).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/mfgtagmostrecent/:num_of_rec', ensureAuthenticated, function(request, response){ //query is in ./dbfunction/mfgtagGetMostRecent
    getMostRecentTags(request.params.num_of_rec).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/mfgtagmaxchangeorder/:jobid', ensureAuthenticated, function(request, response){ //query is in ./dbfunction/mfgtagGetMostRecent
    getMaxChangeOrder(request.params.jobid).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/mfgreqgetchangeorders/:jobid', ensureAuthenticated, function(request, response){ 
    getChangeOrderForJobNumber(request.params.jobid).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/mfgreqgetchangeordersforpackout/:jobid', ensureAuthenticated, function(request, response){ 
    getChangeOrderForJobNumberForPackout(request.params.jobid).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgreqcreatenewreq/', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagCreateTag
    //let statusbody = JSON.parse(request.body);
    createNewReq(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgreqpkcreatereq/', ensureAuthenticated, function(request, response){ 
    createPKReq(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgreqpkupdatereq/', ensureAuthenticated, function(request, response){ 
    updatePKReq(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgrequpdatereq/', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagCreateTag
    updateRequest(request.body).then((data) => {
        //console.log("***** from inside the app.post for updateRequest.")
        //console.log(data)
        return response.status(200).send(data)
    });
});


app.get('/api/mfgtags/allrequests/', ensureAuthenticated, function(request, response){ 
    getAllRequests(request.query.offset, request.query.num_of_recs).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/floorusers/', ensureAuthenticated, function(request, response){ 
    getFloorUsers(request.query.offset, request.query.num_of_recs).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/pms/',  function(request, response){ //ensureAuthenticated,
    getPMs(request.query.offset, request.query.num_of_recs).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/searchrequests/',  function(request, response){ //ensureAuthenticated,
    //getSearchRequests(request.body).then((data) =>{ //old mssql version
    getMFGRequests(request.body).then((data) =>{    
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgrequestsstandalone/', ensureAuthenticated, function(request, response){ 
    getMFGRequestStandAlone(request.body).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/mfgreqgetsinglereq/:reqID', function(request, response){ 
    getSingleMFGRequest(request.params.reqID).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/pkreqgetsinglereq/:reqID', function(request, response){ 
    getSinglePKRequest(request.params.reqID).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/shreqgetsinglereq/:reqID', function(request, response){ 
    getSingleSHRequest(request.params.reqID).then((data) =>{
        response.json(data);
    });
});


app.post('/api/mfgtags/tagsformfgrequests/', ensureAuthenticated, function(request, response){ 
    getTagsForMFGRequest(request.body).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/searchpackoutrequests/',  function(request, response){ //ensureAuthenticated,
    //getSearchPackoutRequests(request.body).then((data) =>{ //old mssql version
    getPKRequests(request.body).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/searchofrequests/',  function(request, response){ //ensureAuthenticated,
    getOFRequests(request.body).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/searchprintrequests/',  function(request, response){ //ensureAuthenticated,
    getPrintRequests(request.body).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/searchpreprequests/',  function(request, response){ //ensureAuthenticated,
    getPrepRequests(request.body).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/searchplanorequests/',  function(request, response){ //ensureAuthenticated,
    getPlanoRequests(request.body).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/mfgreqpkinfo/:pkreqID', ensureAuthenticated, function(request, response){   
    getPackoutRequestInfo(request.params.pkreqID).then((data) =>{
        response.json(data);
    });
});


app.get('/api/mfgtags/mfgreqpkdocs/:jobid', ensureAuthenticated, function(request, response){   
    console.log("&&&&&&&&&& This is request.params.jobid: ", request.params.jobid)
    getPackoutFiles(request.params.jobid).then((data) =>{
        response.json(data);
    });
});


app.get('/api/mfgtags/mfgtagjobinfonotag/:jobid', ensureAuthenticated, function(request, response){   
    getJobInfoNotag(request.params.jobid).then((data) =>{
        response.json(data);
    });
});


app.get('/api/mfgtags/mfgtagmostrecentjobs/:num_of_rec', ensureAuthenticated, function(request, response){ //query is in ./dbfunction/mfgtagGetMostRecent
    getLatestJobs(request.params.num_of_rec).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/mfgtaggetalldeptsanddevices', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagGetAllDeptDevices
    getAllDeptDevices().then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/alldevices', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagGetAllDeptDevices
    getAllDevices().then((data) =>{
        response.json(data)
    });
});

app.get('/api/mfgtags/alldevicetypes', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagGetAllDeptDevices
    getAllDeviceTypes().then((data) =>{
        response.json(data)
    });
});

app.get('/api/mfgtags/alldevicehistory/:deviceID', ensureAuthenticated, function(request, response){ 
    getAllDeviceHistory(request.params.deviceID).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/alldeptsdevices/:deptID', ensureAuthenticated, function(request, response){ 
    getAllDevicesByDept(request.params.deptID).then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/getallitemsdesc/', ensureAuthenticated, function(request, response){ 
    getAllItemsDesc.getAllItemsDesc().then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/getsubstratesforitems/:shipdate_from', ensureAuthenticated, function(request, response){ 
    getTagsSubstratesPerItemCode(request.params.shipdate_from).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/upsertitemalias/', ensureAuthenticated, function(request, response){ 
    upsertItemAlias(request.body).then((data) =>{
        response.json(data);
    });
});

app.post('/api/mfgtags/inventoryitemref/', ensureAuthenticated, function(request, response){
    inventoryItemRef(request.body).then((data) =>{
        response.json(data);
    })
});

app.post('/api/general/usersautocomplete', function(request, response){//ensureAuthenticated, 
    getUsersForAutoComplete(request.body).then((data) =>{
        response.json(data);
    })
});

// app.post('/api/general/marknotifasread/:username', function(request, response){ //ensureAuthenticated,
//     markNotificationAsRead(comment_id, user).then((data) =>{
//         response.json(data);
//     });
// });

app.get('/api/general/getallnotifications/:username',  function(request, response){ //ensureAuthenticated,
    getAllNotifications(request.params.username).then((data) =>{
        response.json(data);
    });
});

app.post('/api/general/getallcommentsby/', function(request, response){ //ensureAuthenticated,
    const { search_by, author, ref_ID } = request.body;
    getAllCommentsBy({ search_by, author, ref_ID }).then((data) =>{
        response.json(data);
    });
});

app.get('/api/general/getallmessagesbyreference/:reqID', ensureAuthenticated, function(request, response){ 
    getAllMessagesByReference(request.params.reqID).then((data) =>{
        response.json(data);
    });
});

// app.post('/api/general/getallcommentsby/', ensureAuthenticated, function(request, response){ 
//     getAllCommentsBy(request.body).then((data) => {
//         response.json(data);
//     });
// });

app.post('/api/general/insertcomment/',  function(request, response){ //ensureAuthenticated,
    insertComment(request.body).then((data) => {
        response.json(data);
    });
});


app.get('/api/general/getallmessagesusermentioned/:username', ensureAuthenticated, function(request, response){ 
    getAllMessagesUserIsMentionned(request.params.username).then((data) =>{
        response.json(data);
    });
});

app.post('/api/general/genmsgreply/', ensureAuthenticated, function(request, response){ 
    sendMsgReply(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/general/addcomment/',  function(request, response){ //ensureAuthenticated,
    addComment(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/general/upsertdbinfo/', ensureAuthenticated, function(request, response){ 
    upsertUserDbInfo(request.body).then((data) => {
        response.json(data);
    });
});

app.get('/api/general/getalluserdbinfo/', ensureAuthenticated, function(request, response){ 
    getUserDbInfo().then((data) =>{
        response.json(data);
    });
});

app.get('/api/general/getcomments/:refID',  function(request, response){ //ensureAuthenticated,
    getAllComments(request.params.refID).then((data) =>{
        response.json(data);
    });
});

app.post('/api/general/marknotifasread/',  function(request, response){ //ensureAuthenticated,
    const { notifId, user } = request.body; 
    updateNotifAsRead(notifId, user).then((data) =>{
        response.json(data);
    });
});

app.post('/api/general/marknotifasnotread/', function(request, response){ //ensureAuthenticated,
    const { notifId, user } = request.body; 
    updateNotifAsNotRead(notifId, user).then((data) =>{
        response.json(data);
    });
});

app.get('/api/general/updateshipping', function(request, response){ 
    updateShipStatus().then((data) =>{
        response.json(data);
    });
});

app.get('/api/general/allshipments/:job_number', ensureAuthenticated, function(request, response){ 
    getShipInfoByJobNumber(request.params.job_number).then((data) =>{
        response.json(data);
    });
});

app.get('/api/general/allmultiship/:job_number', ensureAuthenticated, function(request, response){ 
    getMultiShipByJobNumber(request.params.job_number).then((data) =>{
        response.json(data);
    });
});

app.post('/api/general/createpackingslip/', ensureAuthenticated, function(request, response){ 
    createPackingSlip(request.body).then((data) =>{
        response.json(data);
    });
});


app.get('/api/general/dailyshippingreport', function(request, response){ 
    dailyShippingReport().then((data) =>{
        response.json(data);
    });
});


app.get('/api/general/sendtestemail', function(request, response){ 
    sendTestEmail().then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/allqrcodes', function(request, response){ 
    generateQRCodeAll().then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/allqrcodespk', function(request, response){ 
    generateQRCodePKAll().then((data) =>{
        response.json(data);
    });
});

app.get('/api/mfgtags/qrcodepk/:request_PK_ID', function(request, response){ 
    generateQRCodePK(request.params.request_PK_ID).then((data) =>{
        response.json(data);
    });
});


app.get('/api/mfgtags/alldepts/', ensureAuthenticated, function(request, response){
    getAllDepts().then((data) =>{
        response.json(data);
    })
})


app.get('/api/inventory/allitems/', ensureAuthenticated, function(request, response){ 
    getInventoryItems(request.query.offset, request.query.num_of_recs).then((data) =>{
        response.json(data);
    });
});

app.get('/api/inventory/totitems/', ensureAuthenticated, function(request, response){ 
    getTotalItems().then((data) =>{
        response.json(data);
    });
});

app.get('/api/inventory/searchitems/', ensureAuthenticated, function(request, response){ 
    getInventorySearchItems(request.query.offset, request.query.num_of_recs, request.query.sf, request.query.st).then((data) =>{
        response.json(data);
    });
});

app.get('/api/inventory/totsearchitems/', ensureAuthenticated, function(request, response){ 
    getTotalSearchItems(request.query.sf, request.query.st).then((data) =>{
        response.json(data);
    });
});

app.get('/api/inventory/itemcodes', ensureAuthenticated, function(request, response){
    getInventoryItemCodes().then((data) =>{
        response.json(data);
    });
})

app.get('/api/inventory/newitemcode', ensureAuthenticated, function(request, response){
    getInventoryNewItemCode(request.query.id).then((data)=>{
        response.json(data);
    })
})

app.get('/api/inventory/alluomcode', ensureAuthenticated, function(request, response){
    getInventoryUOMCodes().then((data)=>{
        response.json(data);
    })
})

app.put('/api/inventory/invnewitemreq', ensureAuthenticated, function(request, response){
    createNewItemRequest(request.body).then((data) =>{
        response.json(data);
    })
})

app.get('/api/inventory/getnewitemrequest/:requestid', ensureAuthenticated, function(request, response){ 
    getNewItemRequest(request.params.requestid).then((data) =>{
        response.json(data);
    });
});

app.post('/api/reports/excludefromrebate/', ensureAuthenticated, function(request, response){ 
    getExcludeRebate(request.body).then((data) =>{
        response.json(data);
    });
});

app.get('/api/reports/listofreports/', ensureAuthenticated, function(request, response){ 
    getAllReports().then((data) =>{
        response.json(data);
    });
});

app.get('/api/reports/allsalesrep/', ensureAuthenticated, function(request, response){ 
    getAllSalesRep().then((data) =>{
        response.json(data);
    });
});


/***SHIPPING***/
app.get('/api/mfgtags/mfgreqgetpkforshipping/:jobid', ensureAuthenticated, function(request, response){
    getPKForJobNumberForShipping(request.params.jobid).then((data) =>{
         response.json(data);
    });
});

app.get('/api/reports/invoicedetails/:invNumber', ensureAuthenticated, function(request, response){ 
    getInvoiceDetails(request.params.invNumber).then((data) =>{
        response.json(data);
    });
});


app.post('/api/mfgtags/mfgreqshcreatereq/', ensureAuthenticated, function(request, response){ 
    createSHReq(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgreqshupdatereq/', ensureAuthenticated, function(request, response){ 
    updateSHReq(request.body).then((data) => {
       response.json(data);
    });
});

app.get('/api/reports/bluecostdetails/:jobNumber', ensureAuthenticated, function(request, response){ 
    getBlueCostDetails(request.params.jobNumber).then((data) =>{
        response.json(data);
    });
});

app.get('/api/getsephorarow/:rowID', ensureAuthenticated, function(request, response){ 
    getPRJMGNTSephoraRow(request.params.rowID).then((data) =>{
        response.json(data);
    });
});

app.get('/api/pm/getallsephoramasterstatustracker/', function(request, response){ 
    getAllSephoraMasterStatus(request.params.rowID).then((data) =>{
        response.json(data);
    });
});

app.get('/api/getprjmgntformfieldoptions/', ensureAuthenticated, function(request, response){ 
    getPRJMGNTFormFieldsOptions().then((data) =>{
        response.json(data);
    });
});

app.get('/api/pm/getprjmgntformfieldoptions/', function(request, response){ 
    getPRJMGNTFormFieldsOptions().then((data) =>{
        response.json(data);
    });
});

app.post('/api/pm/upsertpmgnt/', function(request, response){ 
    upsertProject(request.body).then((data) =>{
        response.json(data);
    });
});


app.post('/api/mfgtags/searchshippingrequests/',  function(request, response){ //ensureAuthenticated,
    //getSearchShippingRequests(request.body).then((data) =>{ //mssql methode
    getSHRequests(request.body).then((data) =>{
        response.json(data);
    });
});

app.post('/api/searchprjmgntrequests/', ensureAuthenticated, function(request, response){ 
    getPRJMGNTRequests(request.body).then((data) =>{
        response.json(data);
    });
});

app.post('/api/pm/getall/', function(request, response){ 
    getPRJMGNTRequests(request.body).then((data) =>{
        response.json(data);
    });
});
 
app.post('/api/mfgtags/mfgreqshupdatereq/', ensureAuthenticated, function(request, response){
    updateSHReq(request.body).then((data) => {
        response.json(data);
    });
});

app.post('/api/mfgtags/mfgreqshupdatestatus/',  function(request, response){ //ensureAuthenticated,
    updateReqSHstatus(request.body).then((data) => {
        response.json(data);
    });
});

// app.post('/api/mfgtags/searchshippingrequests/', ensureAuthenticated, function(request, response){ 
//     getSearchShippingRequests(request.body).then((data) =>{
//         response.json(data);
//     });
// });

app.post('/api/estimates/estimateslist/', ensureAuthenticated, function(request, response){ 
    getAllEstimates(request.body).then((data) => {
       response.json(data);
    });
});

app.post('/api/estimates/estimatecreate/', ensureAuthenticated, function(request, response){ 
    createNewEstimate(request.body).then((data) => {
       response.json(data);
    });
});

app.get('/api/general/allclient/', ensureAuthenticated, function(request, response){ 
    getAllClient().then((data) =>{
        response.json(data);
    });
});

app.post('/api/general/upserttablesettings/', function(request, response){ 
    upsertTableSetting(request.body).then((data) => {
       response.json(data);
    });
});

app.post('/api/general/gettablesettings/', function(request, response){ 
    getTableSettingsByTemplate(request.body).then((data) => {
       response.json(data);
    });
});

app.post('/api/general/gettablefields/', function(request, response){ 
    getTableFields(request.body).then((data) => {
       response.json(data);
    });
});

app.post('/api/mfgtags/searchtagsformfgpg/', function(request, response){ 
    getTagsData(request.body).then((data) => {
       response.json(data);
    });
});

app.post('/api/mfgtags/searchtagsrequests/', function(request, response){ 
    getTagRequests(request.body).then((data) => {
       response.json(data);
    });
});

app.post('/api/pm/searchsephoraimports/', function(request, response){ 
    getSephoraImports(request.body).then((data) => {
       response.json(data);
    });
});


app.post('/api/genupsertreq/', function(request, response){ 
    const { req_type, data, current_user } = request.body;
    upsertSwitch(req_type, data, current_user).then((new_data) => {
       response.json(new_data);
    });
});

app.post('/api/getsinglereqbypk/', function(request, response){ 
    getSingleRequestFromSearchIndex(request.body).then((data) => {
       response.json(data);
    });
});

app.get('/api/getjobandcoinfobyjn/:jobid', ensureAuthenticated, function(request, response){   
    getJobAndCOInfoByJn(request.params.jobid).then((data) =>{
        response.json(data);
    });
});

app.get('/api/getlfbyjn/:jobid', ensureAuthenticated, function(request, response){   
    getMFGByJn(request.params.jobid).then((data) =>{
        response.json(data);
    });
});


app.get('/api/getauditbyid/:jobid', ensureAuthenticated, function(request, response){   
    getAuditByID(request.params.jobid).then((data) =>{
        response.json(data);
    });
});


app.get('/api/general/getalltablenames', ensureAuthenticated, function(request, response){
    getTableNames(request.params.jobid).then((data) =>{
        response.json(data);
    });
});

// GET saved views for user and table
app.get('/api/saved-views/:username/:tablename', ensureAuthenticated, function(request, response){
    savedViewsDb.getSavedViews(config.pool, request.params.username, request.params.tablename).then((data) =>{
        response.json(data);
    });
});

// POST create new saved view
app.post('/api/saved-views', ensureAuthenticated, function(request, response){
    savedViewsDb.createSavedView(config.pool, request.body).then((data) =>{
        response.json(data);
    });
});

// DELETE saved view by ID
app.delete('/api/saved-views/:id/:username', ensureAuthenticated, function(request, response){
    savedViewsDb.deleteSavedView(config.pool, request.params.id, request.params.username).then((data) =>{
        response.json(data);
    });
});


// app.get('/api/general/searchad/', function(request, response){
//     getAllClient().then((data) =>{
//         response.json(data);
//     });
// });

/* 
app.put('/api/mfgtags/mfgtagdeptupdatestatus/', ensureAuthenticated, function(request, response){ //query is in ./dbfunctions/mfgtagUpdateDeptStatus
    //let statusbody = JSON.parse(request.body);
    updateDept.updateDeptStatus(request.body).then((data) => {
        response.json(data);
    });
});
*/


// var cjob = new CronJob(
//     '*/30 * * * * *',function(){
//         runMergeStoredProc();
//     })
// cjob.start();


// var cjob = new CronJob(
//     '* * * * * *',function(){
//        updateShipStatus();
//     })
// cjob.start();







var options = { 
    key: fs.readFileSync(process.env.SSL_KEY_PATH), 
    cert: fs.readFileSync(process.env.SSL_CERT_PATH), 
    requestCert: false, 
    rejectUnauthorized: false 
}

var  port = process.env.PORT || 3000;
var server = https.createServer(options, app).listen(3000, function(){
    console.log('ImtechGraphics API is runnning at ' + port);
    console.log('*** Just as reference, below is config *** ')
    console.log(config)
});


