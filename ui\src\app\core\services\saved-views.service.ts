import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface SavedView {
  id?: number;
  name: string;  // Alias for view_name for frontend compatibility
  view_name: string;
  sortState: any;  // Alias for sort_state
  sort_state: any;
  groupingState: string;  // Alias for grouping_state
  grouping_state: string;
  filtersState: any;  // Alias for filters_state
  filters_state: any;
  created_at?: string;
  updated_at?: string;
}

export interface CreateSavedViewRequest {
  userName: string;
  tableName: string;
  viewName: string;
  sortState: any;
  groupingState: string;
  filtersState: any;
}

@Injectable({
  providedIn: 'root'
})
export class SavedViewsService {
  private apiUrl = `${environment.baseurl}/api/saved-views`;

  constructor(private http: HttpClient) {}

  /**
   * Get all saved views for a user and table
   */
  getSavedViews(userName: string, tableName: string): Observable<SavedView[]> {
    const encodedTableName = encodeURIComponent(tableName);
    return this.http.get<SavedView[]>(`${this.apiUrl}/${userName}/${encodedTableName}`);
  }

  /**
   * Create a new saved view
   */
  createSavedView(viewData: CreateSavedViewRequest): Observable<SavedView> {
    return this.http.post<SavedView>(this.apiUrl, viewData);
  }

  /**
   * Delete a saved view
   */
  deleteSavedView(viewId: number, userName: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${viewId}/${userName}`);
  }

  /**
   * Get current user name from localStorage or session
   * TODO: Replace with actual user service
   */
  getCurrentUserName(): string {
    // For now, get from localStorage or use a default
    // In production, this should come from your authentication service
    return localStorage.getItem('currentUser') || 'default_user';
  }
}
