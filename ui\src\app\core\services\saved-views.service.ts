import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface SavedView {
  id?: number;
  userName?: string;     // Required for create, not for response
  tableName?: string;    // Required for create, not for response
  name: string;          // Alias for view_name for frontend compatibility
  view_name: string;
  viewName?: string;     // Alias for view_name
  sortState: any;        // Alias for sort_state
  sort_state: any;
  groupingState: string; // Alias for grouping_state
  grouping_state: string;
  filtersState: any;     // Alias for filters_state
  filters_state: any;
  frozenColumns?: any;   // Alias for frozen_columns
  frozen_columns?: any;
  created_at?: string;
  updated_at?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SavedViewsService {
  private apiUrl = `${environment.baseurl}/api/saved-views`;

  constructor(private http: HttpClient) {}

  /**
   * Get all saved views for a user and table
   */
  getSavedViews(userName: string, tableName: string): Observable<SavedView[]> {
    const encodedTableName = encodeURIComponent(tableName);
    return this.http.get<SavedView[]>(`${this.apiUrl}/${userName}/${encodedTableName}`);
  }

  /**
   * Create a new saved view
   */
  createSavedView(viewData: SavedView): Observable<SavedView> {
    return this.http.post<SavedView>(this.apiUrl, viewData);
  }

  /**
   * Update an existing saved view
   */
  updateSavedView(viewId: number, userName: string, viewData: SavedView): Observable<SavedView> {
    return this.http.put<SavedView>(`${this.apiUrl}/${viewId}/${userName}`, viewData);
  }

  /**
   * Delete a saved view
   */
  deleteSavedView(viewId: number, userName: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${viewId}/${userName}`);
  }

  /**
   * Get current user name from localStorage or session
   * TODO: Replace with actual user service
   */
  getCurrentUserName(): string {
    // For now, get from localStorage or use a default
    // In production, this should come from the authentication service
    return localStorage.getItem('displayname') || 'default_user';
  }
}
