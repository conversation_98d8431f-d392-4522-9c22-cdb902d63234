import { Component, StaticProvider } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { NotificationService } from '../../../core/services/notification.service';
import { HttpClientModule } from '@angular/common/http';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { MfgRequest } from '../../../core/models/mfg-request';
import { Tags } from '../../../core/models/tag';
import { MfgService } from '../../../core/services/request_mfg.service';
import { MfgRequestsFormComponent } from '../mfg-requests-form/mfg-requests-form.component';
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { TagsRequestsListComponent } from '../../tag-requests/tags-requests-list/tags-requests-list.component';
import { ComponentType } from '@angular/cdk/overlay';
import { TableSettingsService } from '../../../core/services/table-settings.service';

@Component({
  selector: 'app-mfg-requests-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, ListToolbarComponent, GenericTableComponent, TagsRequestsListComponent],
  templateUrl:'./mfg-requests-list.component.html',
  providers: [TableSettingsService] 
})

export class MfgRequestsListComponent 
  extends BaseEntityListComponent<MfgRequest> {

  TagsRequestsListComponent = TagsRequestsListComponent;

  /** Dialog form shown when creating / editing a row */
  editComponent: ComponentType<any> = MfgRequestsFormComponent;
  /** Used for localStorage keys and titles */
  tableName = 'MFG Request Table';
  requestType = 'MFG'

  extraProviders = (row: MfgRequest): StaticProvider[] => [
    /* always inject the whole row – most detail panes want that */
    { provide: 'ROW', useValue: row },
  
    { provide: 'JOB', useValue: row.job_number },
    { provide: 'CO',  useValue: row.chg_order }
  ];  

  displayedColumns: (keyof MfgRequest | 'actions')[] = [
    'actions',
    'requestID','ship_date','created_on', 'Client', 'imtechPM','Job_Description','Request_Description','r_status','request_type','total_pdfs','total_substrates', 'total_prints',
    'lamination', 'mounting', 'handwork', 'packing', 'packing_location', 'packing_estimate', 'active_tags', 'print_estimate', 'cutting_estimate', 'notes' 
  ];


  constructor(
    public thisService: MfgService,
    dialog: Dialog,
    savedViewsService: SavedViewsService,
    notificationService: NotificationService
  ) {
    super(dialog, savedViewsService, notificationService);          // forward services to the base class
  }

  onVisibleRows(rows: Array<MfgRequest | any>): void {
    this.tempArray = rows.filter(r => !r.__group && !r.__detail);
  }
}
