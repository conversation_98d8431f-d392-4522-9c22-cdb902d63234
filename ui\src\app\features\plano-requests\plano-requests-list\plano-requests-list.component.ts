import { Component  } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { NotificationService } from '../../../core/services/notification.service';
import { HttpClientModule } from '@angular/common/http';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { PlanoRequest } from '../../../core/models/pi-request';
import { PlanoService } from '../../../core/services/request_pi.service';
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { PlanoRequestsFormComponent } from '../plano-requests-form/plano-requests-form.component'; 
import { TableSettingsService } from '../../../core/services/table-settings.service';

@Component({
  selector: 'app-plano-requests-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, GenericTableComponent, ListToolbarComponent],
  templateUrl: './plano-requests-list.component.html',
  styleUrl: './plano-requests-list.component.scss',
  providers: [TableSettingsService] 
})
export class PlanoRequestsListComponent 
  extends BaseEntityListComponent<PlanoRequest>{

  /** Dialog form shown when creating / editing a row */
  editComponent = PlanoRequestsFormComponent;
  /** Used for localStorage keys and titles */
  tableName = 'PI Request Table';
  requestType = 'PI';


  displayedColumns: (keyof PlanoRequest | 'actions')[] = 
  [ 'actions',
    'plano_id',
    'job_number',
    'change_orders',
    'customer_name',
    'work_type_needed',
    'job_description',
    'plano_request_description',
    'status',
    'due_date_for_plano',
    'number_of_pdfs',
    'assigned_to',
    'notes',
    'created_on',
    'created_by',
    'last_modified_by',
    'last_modified_on'
  ];

  constructor(
    public thisService: PlanoService,
    dialog: Dialog,
    savedViewsService: SavedViewsService,
    notificationService: NotificationService
  ) {
    super(dialog, savedViewsService, notificationService);          // forward services to the base class
  }
}
