import { Component, St<PERSON>Provider } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { NotificationService } from '../../../core/services/notification.service';
import { HttpClientModule } from '@angular/common/http';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { ProjectManagement } from '../../../core/models/project-management';
import { ProjectManagementService } from '../../../core/services/project-management.service'
import { PmRequestFormComponent } from '../pm-request-form/pm-request-form.component'
import { SephoraImportsListComponent } from '../../sephora-imports/sephora-imports-list/sephora-imports-list.component'
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { TagsRequestsListComponent } from '../../tag-requests/tags-requests-list/tags-requests-list.component';
import { ComponentType } from '@angular/cdk/overlay';
import { TableSettingsService } from '../../../core/services/table-settings.service';
import { StickyScrollBarComponent } from '../../../shared/components/sticky-scrollbar/sticky-scrollbar.component';
import { SearchRelayService } from '../../../core/services/search-relay.service';


@Component({
  selector: 'app-pm-request-list',
  standalone: true,
  imports: [CommonModule, DialogModule, ListToolbarComponent, GenericTableComponent, SephoraImportsListComponent, StickyScrollBarComponent],
  templateUrl: './pm-request-list.component.html',
  styleUrl: './pm-request-list.component.scss',
  providers: [TableSettingsService, SearchRelayService] 
})
export class PmRequestListComponent
  extends BaseEntityListComponent<ProjectManagement>{

  PmRequestListComponent = PmRequestListComponent;

  SephoraImportsListComponent = SephoraImportsListComponent
  
  /** Dialog form shown when creating / editing a row */
  editComponent: ComponentType<any> = PmRequestFormComponent;
  /** Used for localStorage keys and titles */
  tableName = 'PM Request Table';
  requestType = 'PM'

  extraProviders = (row: ProjectManagement): StaticProvider[] => [
  /* always inject the whole row – most detail panes want that */
  { provide: 'ROW', useValue: row },

  /* add anything that pane X / Y / Z might need */
  { provide: 'REFS',     useValue: row.references_data ?? [] },
  // { provide: 'BRAND',    useValue: row.brand           },
  // { provide: 'JOBTITLE', useValue: row.jobtitle        }
];

  rowKeyFn = (row: ProjectManagement): string =>
    String(row.references_data);  

  displayedColumns: (keyof ProjectManagement | 'actions')[] = [
      'actions',
      'projectmgntrecid',
      'imtechjobnumber',
      'references_data',
      'jobtitle',
      'projectmgr',
      'acctrep',
      'brand',
      'retailer',
      'retailer2',
      'category',
      'displayvendor',
      'parentcompany',
      'projecttype',
      'animationtype',
      'graphiconly',
      'sizeofupdate',
      'packouttype',
      'installs',
      'funded',
      'deliverytype',
      'bulkdeliverydate',
      'collateddeliverydate',
      'oncounterdate',
      'protoawdue',
      'protoproofs',
      'protoreviewdate',
      'awreleasedate',
      'proofsdue',
      'brandapprovaldate',
      'displayapprovaldate',
      'mfgstartdate',
      'sephora_row_id',
      
    ];

    constructor(
      public thisService: ProjectManagementService,
      dialog: Dialog,
      savedViewsService: SavedViewsService,
      notificationService: NotificationService
    ) {
        super(dialog, savedViewsService, notificationService);          // forward services to the base class
      }

    onVisibleRows(rows: Array<ProjectManagement | any>): void {
        // strip out group-header / detail rows before counting
        this.tempArray = rows.filter(r => !r.__group && !r.__detail);
      }

}
