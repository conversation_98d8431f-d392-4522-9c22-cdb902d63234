<!-- header -->
<app-list-toolbar
  [savedViews]="savedViews"
  [selectedViewId]="selectedViewId"
  [tempArrayLength]="tempArray.length"
  [requestType]="requestType"
  [lastUpdatedTime]="lastUpdatedTime"
  (create)="openNewItem()"
  (openSettings)="openTableSettingsFromCurrent()"
  [showSaveAsViewButton]="true"
  [savedCustomViews]="savedCustomViews"
  (saveAsView)="onSaveAsView()"
  (customViewSelected)="onCustomViewSelected($event)"
  (viewSelected)="genericTable.onViewSelectionChange($event)">
</app-list-toolbar>
  <!-- header end -->

<!-- table start -->
 <div>
    <app-generic-table
        #genericTable
        [requestType]="requestType"
        [tableName]="tableName"
        [displayedColumns]="displayedColumns"
        [dataService]="thisService"
        [showDetailColumn]=false
        [editComponent]="editComponent"  
        (dataLoaded)="onDataLoaded($event)"
        (viewsLoaded)="onViewsLoaded($event)"
        (savedViewId) = "onSavedViewID($event)"
        >
    </app-generic-table> 
</div>
<!-- table end -->