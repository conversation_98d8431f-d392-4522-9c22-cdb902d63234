import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { NotificationService } from '../../../core/services/notification.service';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { PrepRequest } from '../../../core/models/prep-request';
import { PrepService } from '../../../core/services/request_pe.service';
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { PrepRequestsFormComponent } from '../prep-requests-form/prep-requests-form.component';
import { TableSettingsService } from '../../../core/services/table-settings.service';

@Component({
  selector: 'app-prep-requests-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, GenericTableComponent, ListToolbarComponent],
  templateUrl: './prep-requests-list.component.html',
  styleUrl: './prep-requests-list.component.scss',
  providers: [TableSettingsService] 
})


export class PrepRequestsListComponent 
extends BaseEntityListComponent<PrepRequest> {

  /** Dialog form shown when creating / editing a row */
  editComponent = PrepRequestsFormComponent;
  /** Used for localStorage keys and titles */
  tableName = 'PE Request Table'; //Preps
  requestType = 'PE'


  displayedColumns: (keyof PrepRequest | 'actions')[] = 
  [ 'actions',
    'prep_id',
    'job_number',
    'change_orders',
    'customer_name',
    'mfg_request',
    'job_description',
    'prep_request_description' ,
    'status',
    'due_date_for_prep',
    'number_of_pdfs',
    'assigned_to',
    'notes',
    'created_by',
    'created_on',
    'last_modified_by',
    'last_modified_on'
  ];

  constructor(
    public thisService: PrepService,
    dialog: Dialog,
    savedViewsService: SavedViewsService,
    notificationService: NotificationService
  ) {
    super(dialog, savedViewsService, notificationService);          // forward services to the base class
  }
}
