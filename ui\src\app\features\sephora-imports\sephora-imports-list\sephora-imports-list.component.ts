import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { NotificationService } from '../../../core/services/notification.service';
import { HttpClientModule } from '@angular/common/http';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { SephoraImport } from '../../../core/models/project-management';
import { SephoraImportService } from '../../../core/services/sephora-imports.service';
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { TagsRequestsListComponent } from '../../tag-requests/tags-requests-list/tags-requests-list.component';
import { ComponentType } from '@angular/cdk/overlay';
import { TableSettingsService } from '../../../core/services/table-settings.service';
import { MfgRequestsFormComponent } from '../../mfg-requests/mfg-requests-form/mfg-requests-form.component';
import { PmRequestFormComponent } from '../../pm-requests/pm-request-form/pm-request-form.component';
import { request_search } from '../../../core/models/tag';

@Component({
  selector: 'app-sephora-imports-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, ListToolbarComponent, GenericTableComponent, TagsRequestsListComponent],
  templateUrl: './sephora-imports-list.component.html',
  styleUrl: './sephora-imports-list.component.scss',
  providers: [TableSettingsService, SephoraImportService] 
})
export class SephoraImportsListComponent 
 extends BaseEntityListComponent<SephoraImport> implements OnInit{

  SephoraImportsListComponent = SephoraImportsListComponent

  editComponent: ComponentType<any> = PmRequestFormComponent;

    /** Used for localStorage keys and titles */
    tableName = 'Sephora Import Table';
    requestType = 'Sephora'

  displayedColumns: (keyof SephoraImport | 'actions')[] = [
    'actions',
    'sephora_row_id',
    'cancelled',
    'sephora_funding',
    'imtech_master_job_id',
    'fixture_status',
    'fixture_phase',
    'graphics_received_for_all_assortments',
    'graphic_status',
    'graphic_phase',
    'story_fixture',
    'translation_status',
    'project_owner',
    's_class',
    'country',
    'brand',
    'season_product_description',
    'graphic_contact',
    'fixture_vendor',
    'fixture_contact',
    'ship_date',
    'fixture_land_date',
    'execution_week',
    'kick_off_call',
    'rd1_initial_renderings_and_engineering',
    'renderings_and_engineering_approved',
    'proto_review_and_initial_pricing_due',
    'proto_and_pricing_approved_final_dielines_due',
    'final_distro_due',
    'undermount_art_upload',
    'undermount_art_approved',
    'paper_art_upload',
    'all_art_approved',
    'undermount_art_due_to_fixture_co',
    'paper_art_due_to_fixture_co',
    'instructions_due_for_review',
    'final_renderings_and_instructions_due',
    'skincare_acrylic_assortments',
    'skincare_graphic_assortments',
    'skincare_sku_assortments',
    'skincare_department',
    'imtechpm',
    'imtechacctexec',
    'imtechstatusgroup'
  ];

  constructor(
    public thisService: SephoraImportService,
    dialog: Dialog,
    savedViewsService: SavedViewsService,
    notificationService: NotificationService,
    @Optional() @Inject('REFS') public refs_data?: string,
    @Optional() @Inject('ROW') public masterRow?: any
    ) {
      super(dialog, savedViewsService, notificationService);          // forward services to the base class
    }

    request_search: request_search = {
            search_keyword  : '',
            search_pm       : '',
            search_startDate: '',
            search_endDate  : '',
            search_hideComplete: 'false',
            search_showAll     : 'false',
            /* Job‑specific fields (optional) */
            search_job_number  : '',
            search_chg_order   : undefined as unknown as number,
            search_refs_data: {}
          };

  ngOnInit(): void {
     /* ── expansion panel case ─────────────────────────── */
     console.log("@@@@@@@@@@@@@@@@@@@@@@@@@@@ ngOnInit() in SephoraImportsListComponent")
      if (this.refs_data ) {
        // console.log('**** from inside if (this.job_number && this.chg_order) of ngOnInit() of TagsRequestsListComponent *****')
        // console.log("this.job_number: ", this.job_number)
         console.log("this.refs_data: ", this.refs_data)
        // this.thisService.initForJob(this.job_number, this.chg_order);       // or put them in a signal
        this.request_search.search_refs_data = this.refs_data
        //this.thisService.getAllRequests(this.request_search); 
        console.log('!!!!!!!!!!!!!!! This.request_search !!!!!!!!!!: ', this.request_search)
      } 
    

      
  }
  
    onVisibleRows(rows: Array<SephoraImportService | any>): void {
      // strip out group-header / detail rows before counting
      this.tempArray = rows.filter(r => !r.__group && !r.__detail);
    }

}
