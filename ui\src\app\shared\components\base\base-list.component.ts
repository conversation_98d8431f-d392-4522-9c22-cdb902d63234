import { ViewChild, Directive } from '@angular/core';
import { Dialog } from '@angular/cdk/dialog';
import { ComponentType } from '@angular/cdk/portal';
import { GenericTableComponent } from '../generic-table/generic-table.component';
import {
  TableSettingsFromDB,
  TableColumnSetting,
} from '../../../core/models/table-settings';
import {
  SaveViewDialogComponent,
  SaveViewDialogData,
  SaveViewDialogResult
} from '../save-view-dialog/save-view-dialog.component';
import { SavedViewsService, SavedView } from '../../../core/services/saved-views.service';

/**
 * Base component that bundles all repeated “table + settings” logic.
 * Child components only set:
 *   • editComponent → the dialog form component
 *   • tableName     → name used for localStorage keys
 *
 * NOTE: T must expose { id: number } because GenericTableComponent<T> requires it.
 */
@Directive()
export abstract class BaseEntityListComponent<
  T extends { id: number }       
> {

  /* values provided by the child component */
  abstract editComponent: ComponentType<any>;
  abstract tableName: string;

  /* shared state */
  @ViewChild('genericTable') genericTable!: GenericTableComponent<T>;

  tempArray: T[] = [];
  lastUpdatedTime = '';
  savedViews: TableSettingsFromDB[] = [];
  selectedViewId: number | null = null;
  savedCustomViews: SavedView[] = [];
  currentAppliedView: string = '';
  private savedViewsLoaded: boolean = false;

  constructor(protected dialog: Dialog, protected savedViewsService: SavedViewsService) {
    // loadSavedCustomViews() will be called in ngOnInit when tableName is available
  }

  /* handlers */
  onDataLoaded(rows: T[]): void {
    this.tempArray       = rows;
    this.lastUpdatedTime = new Date().toLocaleTimeString();
    // Load saved views only once when data is first loaded (tableName is now available)
    if (!this.savedViewsLoaded) {
      this.loadSavedCustomViews();
    }
  }

  onViewsLoaded(views: TableSettingsFromDB[]): void {
    this.savedViews = views;
  }

  onSavedViewID(viewid: any): void {
    this.selectedViewId = Number(viewid);
  }

  /* Handle view selection from dropdown */
  onViewSelected(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const viewId = target.value;

    // Update selectedViewId
    this.selectedViewId = viewId ? Number(viewId) : null;

    // Forward to generic table
    if (this.genericTable) {
      this.genericTable.onViewSelectionChange(event);
    }
  }

  /* open create / edit dialog */
  openNewItem(data: Record<string, unknown> = { actionType: 'create' }): void {
    this.dialog
      .open(this.editComponent, {
        width: '900px',
        maxHeight: '70vh',
        autoFocus: false,
        disableClose: true,
        panelClass: 'overflow-auto',
        data,
      })
      .closed.subscribe(() => {
        console.log('The dialog was closed')
        this.genericTable.reloadAfterDialog();
      });
  }

  /* open “table settings” dialog on the child GenericTable */
  openTableSettingsFromCurrent(): void {
    const viewId = localStorage.getItem(`${this.tableName}_current_setttings_id`);
    let currentSettings: TableColumnSetting | undefined;

    if (viewId) {
      const raw = localStorage.getItem(`${this.tableName}_current_settings`);
      if (raw) {
        try {
          currentSettings = JSON.parse(raw) as TableColumnSetting;
        } catch (err) {
          console.warn('Stored settings were not valid JSON – falling back to defaults.', err);
        }
      }
    }
    this.genericTable.openTableSettings(viewId, currentSettings);
  }

  /* open "save as view" dialog */
  onSaveAsView(): void {
    const dialogData: SaveViewDialogData = {
      tableName: this.tableName,
      currentViewName: this.currentAppliedView || undefined,
      hasExistingViews: this.savedCustomViews.length > 0
    };

    const dialogRef = this.dialog.open(SaveViewDialogComponent, {
      width: '450px',
      data: dialogData
    });

    dialogRef.closed.subscribe((result: unknown) => {
      const typedResult = result as SaveViewDialogResult;
      if (typedResult?.viewName) {
        if (typedResult.isEdit) {
          this.updateExistingView(typedResult.viewName);
        } else {
          this.saveCurrentViewToStorage(typedResult.viewName);
        }
      }
    });
  }

  /* save current view state to localStorage (later will be DB) */
  private saveCurrentViewToStorage(viewName: string): void {
    // Get current state from localStorage
    const sortState = localStorage.getItem(`${this.tableName}_current_sort_state`);
    const groupingState = localStorage.getItem(`${this.tableName}_current_grouping_state`);
    const filtersState = localStorage.getItem(`${this.tableName}_current_filters`);
    const frozenColumns = localStorage.getItem(`${this.tableName}_current_frozen_columns`);

    // Debug localStorage values
    console.log('🔍 RAW VALUES FROM LOCALSTORAGE:');
    console.log('sortState raw:', sortState);
    console.log('groupingState raw:', groupingState);
    console.log('filtersState raw:', filtersState);

    // Safe JSON parsing with error handling
    const parseJsonSafely = (jsonString: string | null, fieldName: string) => {
      if (!jsonString) return null;
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn(`⚠️ ${fieldName} is not JSON, treating as string:`, jsonString);
        // If it's not JSON, return the string value directly (for grouping state)
        if (fieldName === 'groupingState') {
          return jsonString; // Return the string directly for grouping
        }
        return null;
      }
    };

    // Create view object for API
    const viewData: SavedView = {
      userName: this.savedViewsService.getCurrentUserName(),
      tableName: this.tableName,
      name: viewName,
      view_name: viewName,
      viewName: viewName,
      sortState: parseJsonSafely(sortState, 'sortState'),
      sort_state: parseJsonSafely(sortState, 'sortState'),
      groupingState: parseJsonSafely(groupingState, 'groupingState'),
      grouping_state: parseJsonSafely(groupingState, 'groupingState'),
      filtersState: parseJsonSafely(filtersState, 'filtersState'),
      filters_state: parseJsonSafely(filtersState, 'filtersState'),
      frozenColumns: parseJsonSafely(frozenColumns, 'frozenColumns'),
      frozen_columns: parseJsonSafely(frozenColumns, 'frozenColumns')
    };

    //console.log('📦 NEW VIEW OBJECT:', viewData);

    // Save to API
    this.savedViewsService.createSavedView(viewData).subscribe({
      next: (savedView) => {
        console.log('✅ View saved successfully:', savedView);
        alert(`View "${viewName}" saved successfully!`);

        // Set the new view as the current selected view immediately
        localStorage.setItem(`${this.tableName}_last_selected_view`, viewName);
        this.currentAppliedView = viewName;
        console.log(`🎯 Set "${viewName}" as current applied view`);

        // Reload the saved views list (reset flag to allow reload)
        this.savedViewsLoaded = false;
        this.loadSavedCustomViews();
      },
      error: (error) => {
        console.error('❌ Error saving view:', error);
        alert(`Error saving view: ${error.error?.error || error.message}`);
      }
    });
  }

  /* Update an existing view with current state */
  private updateExistingView(viewName: string): void {
    // Find the existing view
    const existingView = this.savedCustomViews.find(view => view.name === viewName);
    if (!existingView) {
      console.error(`View "${viewName}" not found for update`);
      return;
    }

    // Get current state from localStorage
    const sortState = localStorage.getItem(`${this.tableName}_current_sort_state`);
    const groupingState = localStorage.getItem(`${this.tableName}_current_grouping_state`);
    const filtersState = localStorage.getItem(`${this.tableName}_current_filters`);
    const frozenColumns = localStorage.getItem(`${this.tableName}_current_frozen_columns`);

    // Safe JSON parsing
    const parseJsonSafely = (jsonString: string | null, fieldName: string) => {
      if (!jsonString) return null;
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn(`⚠️ ${fieldName} is not JSON, treating as string:`, jsonString);
        if (fieldName === 'groupingState') {
          return jsonString;
        }
        return null;
      }
    };

    // Create update object
    const updateData: SavedView = {
      name: viewName,
      view_name: viewName,
      viewName: viewName,
      sortState: parseJsonSafely(sortState, 'sortState'),
      sort_state: parseJsonSafely(sortState, 'sortState'),
      groupingState: parseJsonSafely(groupingState, 'groupingState'),
      grouping_state: parseJsonSafely(groupingState, 'groupingState'),
      filtersState: parseJsonSafely(filtersState, 'filtersState'),
      filters_state: parseJsonSafely(filtersState, 'filtersState'),
      frozenColumns: parseJsonSafely(frozenColumns, 'frozenColumns'),
      frozen_columns: parseJsonSafely(frozenColumns, 'frozenColumns')
    };

    console.log('🔄 Updating existing view:', updateData);

    // Use the proper update endpoint
    this.savedViewsService.updateSavedView(existingView.id!, this.savedViewsService.getCurrentUserName(), updateData).subscribe({
      next: (savedView) => {
        console.log('✅ View updated successfully:', savedView);
        alert(`View "${viewName}" updated successfully!`);

        // Keep the updated view as the current selected view
        localStorage.setItem(`${this.tableName}_last_selected_view`, viewName);
        this.currentAppliedView = viewName;
        console.log(`🎯 Kept "${viewName}" as current applied view after update`);

        // Reload the saved views list
        this.savedViewsLoaded = false;
        this.loadSavedCustomViews();
      },
      error: (error) => {
        console.error('❌ Error updating view:', error);
        alert(`Error updating view: ${error.error?.error || error.message}`);
      }
    });
  }

  /* Load saved custom views from API */
  loadSavedCustomViews(): void {
    if (!this.tableName) {
      console.warn('⚠️ tableName not available yet, skipping loadSavedCustomViews');
      return;
    }

    if (this.savedViewsLoaded) {
      console.log('🔄 Saved views already loaded, skipping...');
      return;
    }

    const userName = this.savedViewsService.getCurrentUserName();
    console.log(`🔍 Loading saved views for "${userName}" and "${this.tableName}"`);

    this.savedViewsService.getSavedViews(userName, this.tableName).subscribe({
      next: (views) => {
        // Map API response to frontend format
        this.savedCustomViews = views.map(view => ({
          ...view,
          name: view.view_name,
          sortState: view.sort_state,
          groupingState: view.grouping_state,
          filtersState: view.filters_state,
          frozenColumns: view.frozen_columns
        }));
        console.log(`✅ Loaded ${this.savedCustomViews.length} saved views:`, this.savedCustomViews);

        // Debug frozen columns
        this.savedCustomViews.forEach(view => {
          console.log(`🧊 View "${view.name}" frozen columns:`, view.frozenColumns);
        });

        // Mark as loaded to prevent future calls
        this.savedViewsLoaded = true;

        // Apply last selected view or fallback to localStorage states
        this.applyLastSelectedViewOrFallback();
      },
      error: (error) => {
        console.error('Error loading saved views:', error);
        this.savedCustomViews = [];

        // Mark as loaded even on error to prevent retry loops
        this.savedViewsLoaded = true;

        // If no saved views, use localStorage states
        this.applyLastSelectedViewOrFallback();
      }
    });
  }

  /* Apply a selected custom view */
  onCustomViewSelected(viewName: string): void {
    const selectedView = this.savedCustomViews.find(view => view.name === viewName);

    if (selectedView) {
      // console.log(`🔄 Applying custom view "${viewName}":`, selectedView);

      // Save this as the last selected view
      localStorage.setItem(`${this.tableName}_last_selected_view`, viewName);
      // console.log(`💾 Saved "${viewName}" as last selected view for ${this.tableName}`);

      // Apply the saved states to localStorage (overriding current values)
      if (selectedView.sortState) {
        localStorage.setItem(`${this.tableName}_current_sort_state`, JSON.stringify(selectedView.sortState));
      } else {
        localStorage.removeItem(`${this.tableName}_current_sort_state`);
      }
      if (selectedView.groupingState) {
        localStorage.setItem(`${this.tableName}_current_grouping_state`, selectedView.groupingState);
      } else {
        localStorage.removeItem(`${this.tableName}_current_grouping_state`);
      }
      if (selectedView.filtersState) {
        localStorage.setItem(`${this.tableName}_current_filters`, JSON.stringify(selectedView.filtersState));
      } else {
        localStorage.removeItem(`${this.tableName}_current_filters`);
      }
      if (selectedView.frozenColumns) {
        localStorage.setItem(`${this.tableName}_current_frozen_columns`, JSON.stringify(selectedView.frozenColumns));
        console.log('🧊 Applied frozen columns:', selectedView.frozenColumns);
      } else {
        localStorage.removeItem(`${this.tableName}_current_frozen_columns`);
        console.log('🧊 Removed frozen columns (view has none)');
      }

      // Reload the table immediately without page refresh
      if (this.genericTable) {
        console.log('🔄 Reloading table data...');
        this.genericTable.loadData(); // loadData() now handles frozen columns automatically
      }

      // Update current applied view
      this.currentAppliedView = viewName;

      // console.log(`✅ Applied custom view "${viewName}" for ${this.tableName}`);
    }
  }

  /* Delete a saved view */
  onDeleteView(view: any): void {
    console.log(`🗑️ Deleting view: "${view.name}"`);

    const userName = this.savedViewsService.getCurrentUserName();

    this.savedViewsService.deleteSavedView(view.id, userName).subscribe({
      next: () => {
        console.log(`✅ View "${view.name}" deleted successfully`);

        // If the deleted view was the current one, clear it
        if (this.currentAppliedView === view.name) {
          this.currentAppliedView = '';
          localStorage.removeItem(`${this.tableName}_last_selected_view`);
        }

        // Reload the saved views list
        this.savedViewsLoaded = false;
        this.loadSavedCustomViews();

        alert(`View "${view.name}" deleted successfully!`);
      },
      error: (error) => {
        console.error('❌ Error deleting view:', error);
        alert(`Error deleting view: ${error.error?.error || error.message}`);
      }
    });
  }

  /* Detect which view is currently applied based on localStorage states */
  detectCurrentAppliedView(): void {
    const currentSortState = localStorage.getItem(`${this.tableName}_current_sort_state`);
    const currentGroupingState = localStorage.getItem(`${this.tableName}_current_grouping_state`);
    const currentFiltersState = localStorage.getItem(`${this.tableName}_current_filters`);

    // Compare with each saved view
    for (const view of this.savedCustomViews) {
      const sortMatch = this.compareStates(view.sortState, currentSortState);
      const groupingMatch = this.compareStates(view.groupingState, currentGroupingState);
      const filtersMatch = this.compareStates(view.filtersState, currentFiltersState);

      // console.log(`- sortMatch: ${sortMatch}, groupingMatch: ${groupingMatch}, filtersMatch: ${filtersMatch}`);

      if (sortMatch && groupingMatch && filtersMatch) {
        this.currentAppliedView = view.name;
        // console.log(`🎯 Detected current view: "${view.name}"`);
        return;
      }
    }

    // No match found
    this.currentAppliedView = '';
    console.log('🔍 No matching saved view detected');
  }

  /* Apply a custom saved view */
  applyCustomView(viewName: string): void {
    const selectedView = this.savedCustomViews.find(view => view.name === viewName);

    if (selectedView) {
      console.log(`🔄 Applying custom view "${viewName}":`, selectedView);

      // Save this as the last selected view
      localStorage.setItem(`${this.tableName}_last_selected_view`, viewName);
      console.log(`💾 Saved "${viewName}" as last selected view for ${this.tableName}`);

      // Apply the saved states to localStorage (overriding current values)
      if (selectedView.sortState) {
        localStorage.setItem(`${this.tableName}_current_sort_state`, JSON.stringify(selectedView.sortState));
      } else {
        localStorage.removeItem(`${this.tableName}_current_sort_state`);
      }
      if (selectedView.groupingState) {
        localStorage.setItem(`${this.tableName}_current_grouping_state`, selectedView.groupingState);
      } else {
        localStorage.removeItem(`${this.tableName}_current_grouping_state`);
      }
      if (selectedView.filtersState) {
        localStorage.setItem(`${this.tableName}_current_filters`, JSON.stringify(selectedView.filtersState));
      } else {
        localStorage.removeItem(`${this.tableName}_current_filters`);
      }
      if (selectedView.frozenColumns) {
        localStorage.setItem(`${this.tableName}_current_frozen_columns`, JSON.stringify(selectedView.frozenColumns));
        console.log('🧊 Applied frozen columns:', selectedView.frozenColumns);
      } else {
        localStorage.removeItem(`${this.tableName}_current_frozen_columns`);
        console.log('🧊 Removed frozen columns (view has none)');
      }

      // Reload the table immediately without page refresh
      if (this.genericTable) {
        console.log('🔄 Reloading table data...');
        this.genericTable.loadData(); // loadData() now handles frozen columns automatically
      }

      // Update the current applied view
      this.currentAppliedView = viewName;

      // console.log(`✅ Applied custom view "${viewName}" for ${this.tableName}`);
    }
  }

  /* Apply last selected view or fallback to localStorage states */
  private applyLastSelectedViewOrFallback(): void {
    // Check if there's a last selected view stored
    const lastSelectedView = localStorage.getItem(`${this.tableName}_last_selected_view`);

    // console.log(`🔍 Last selected view for ${this.tableName}:`, lastSelectedView);

    if (lastSelectedView && this.savedCustomViews.length > 0) {
      // Try to find and apply the last selected view
      const viewToApply = this.savedCustomViews.find(view => view.name === lastSelectedView);

      if (viewToApply) {
        // console.log(`🎯 Applying last selected view: "${lastSelectedView}"`);
        this.applyCustomView(lastSelectedView);
        return;
      } else {
        // console.log(`⚠️ Last selected view "${lastSelectedView}" not found in saved views`);
        // Clear invalid last selected view
        localStorage.removeItem(`${this.tableName}_last_selected_view`);
      }
    }

    // Fallback: Use localStorage states only if no saved views exist
    if (this.savedCustomViews.length === 0) {
      // console.log('📋 No saved views found, using localStorage states as fallback');
      this.currentAppliedView = '';
    } else {
      // console.log('📋 Saved views exist but no last selected view, showing default state');
      this.currentAppliedView = '';
    }
  }

  /* Helper method for robust state comparison */
  private compareStates(viewState: any, localStorageState: string | null): boolean {
    // Handle null/undefined cases
    if (!viewState && !localStorageState) return true;
    if (!viewState || !localStorageState) return false;

    try {
      // If viewState is already a string, compare directly
      if (typeof viewState === 'string') {
        return viewState === localStorageState;
      }

      // If viewState is an object, stringify and compare
      const viewStateString = JSON.stringify(viewState);
      return viewStateString === localStorageState;
    } catch (error) {
      console.warn('Error comparing states:', error);
      return false;
    }
  }
}
