import { ViewChild, Directive } from '@angular/core';
import { Dialog } from '@angular/cdk/dialog';
import { ComponentType } from '@angular/cdk/portal';
import { GenericTableComponent } from '../generic-table/generic-table.component';
import {
  TableSettingsFromDB,
  TableColumnSetting,
} from '../../../core/models/table-settings';
import {
  SaveViewDialogComponent,
  SaveViewDialogData,
  SaveViewDialogResult
} from '../save-view-dialog/save-view-dialog.component';

/**
 * Base component that bundles all repeated “table + settings” logic.
 * Child components only set:
 *   • editComponent → the dialog form component
 *   • tableName     → name used for localStorage keys
 *
 * NOTE: T must expose { id: number } because GenericTableComponent<T> requires it.
 */
@Directive()
export abstract class BaseEntityListComponent<
  T extends { id: number }       
> {

  /* values provided by the child component */
  abstract editComponent: ComponentType<any>;
  abstract tableName: string;

  /* shared state */
  @ViewChild('genericTable') genericTable!: GenericTableComponent<T>;

  tempArray: T[] = [];
  lastUpdatedTime = '';
  savedViews: TableSettingsFromDB[] = [];
  selectedViewId: number | null = null;
  savedCustomViews: any[] = [];

  constructor(protected dialog: Dialog) {
    this.loadSavedCustomViews();
  }

  /* handlers */
  onDataLoaded(rows: T[]): void {
    this.tempArray       = rows;
    this.lastUpdatedTime = new Date().toLocaleTimeString();
  }

  onViewsLoaded(views: TableSettingsFromDB[]): void {
    this.savedViews = views;
  }

  onSavedViewID(viewid: any): void {
    //this.selectedViewId = Number(viewid);
  }

  /* open create / edit dialog */
  openNewItem(data: Record<string, unknown> = { actionType: 'create' }): void {
    this.dialog
      .open(this.editComponent, {
        width: '900px',
        maxHeight: '70vh',
        autoFocus: false,
        disableClose: true,
        panelClass: 'overflow-auto',
        data,
      })
      .closed.subscribe(() => {
        console.log('The dialog was closed')
        this.genericTable.reloadAfterDialog();
      });
  }

  /* open “table settings” dialog on the child GenericTable */
  openTableSettingsFromCurrent(): void {
    const viewId = localStorage.getItem(`${this.tableName}_current_setttings_id`);
    let currentSettings: TableColumnSetting | undefined;

    if (viewId) {
      const raw = localStorage.getItem(`${this.tableName}_current_settings`);
      if (raw) {
        try {
          currentSettings = JSON.parse(raw) as TableColumnSetting;
        } catch (err) {
          console.warn('Stored settings were not valid JSON – falling back to defaults.', err);
        }
      }
    }
    this.genericTable.openTableSettings(viewId, currentSettings);
  }

  /* open "save as view" dialog */
  onSaveAsView(): void {
    const dialogData: SaveViewDialogData = {
      tableName: this.tableName
    };

    const dialogRef = this.dialog.open(SaveViewDialogComponent, {
      width: '400px',
      data: dialogData
    });

    dialogRef.closed.subscribe((result: unknown) => {
      const typedResult = result as SaveViewDialogResult;
      if (typedResult?.viewName) {
        this.saveCurrentViewToStorage(typedResult.viewName);
      }
    });
  }

  /* save current view state to localStorage (later will be DB) */
  private saveCurrentViewToStorage(viewName: string): void {
    // Get current state from localStorage
    const sortState = localStorage.getItem(`${this.tableName}_current_sort_state`);
    const groupingState = localStorage.getItem(`${this.tableName}_current_grouping_state`);
    const filtersState = localStorage.getItem(`${this.tableName}_current_filters`);

    // Debug localStorage values
    console.log('🔍 RAW VALUES FROM LOCALSTORAGE:');
    console.log('sortState raw:', sortState);
    console.log('groupingState raw:', groupingState);
    console.log('filtersState raw:', filtersState);

    // Safe JSON parsing with error handling
    const parseJsonSafely = (jsonString: string | null, fieldName: string) => {
      if (!jsonString) return null;
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn(`⚠️ ${fieldName} is not JSON, treating as string:`, jsonString);
        // If it's not JSON, return the string value directly (for grouping state)
        if (fieldName === 'groupingState') {
          return jsonString; // Return the string directly for grouping
        }
        return null;
      }
    };

    // Create view object
    const savedView = {
      name: viewName,
      tableName: this.tableName,
      sortState: parseJsonSafely(sortState, 'sortState'),
      groupingState: parseJsonSafely(groupingState, 'groupingState'),
      filtersState: parseJsonSafely(filtersState, 'filtersState'),
      createdAt: new Date().toISOString()
    };

    console.log('🔍 OBJET DE LA VUE À ENREGISTRER:');
    console.log('📋 Nom de la vue:', viewName);
    console.log('📊 Table:', this.tableName);
    console.log('🔄 Sort State:', savedView.sortState);
    console.log('📁 Grouping State:', savedView.groupingState);
    console.log('🔍 Filters State:', savedView.filtersState);
    console.log('📅 Created At:', savedView.createdAt);
    console.log('📦 OBJET COMPLET:', savedView);

    // For now, save to localStorage (later will be DB)
    const existingViews = JSON.parse(localStorage.getItem(`${this.tableName}_saved_views`) || '[]');
    existingViews.push(savedView);
    localStorage.setItem(`${this.tableName}_saved_views`, JSON.stringify(existingViews));

    // Show success message
    alert(`View "${viewName}" saved successfully!`);

    // Reload the saved views list
    this.loadSavedCustomViews();
  }

  /* Load saved custom views from localStorage */
  loadSavedCustomViews(): void {
    const savedViewsKey = `${this.tableName}_saved_views`;
    const savedViewsJson = localStorage.getItem(savedViewsKey);

    if (savedViewsJson) {
      try {
        this.savedCustomViews = JSON.parse(savedViewsJson);
      } catch (error) {
        console.error('Error parsing saved views:', error);
        this.savedCustomViews = [];
      }
    }
  }

  /* Apply a selected custom view */
  onCustomViewSelected(viewName: string): void {
    const selectedView = this.savedCustomViews.find(view => view.name === viewName);

    if (selectedView) {
      // Apply the saved states to localStorage (overriding current values)
      if (selectedView.sortState) {
        localStorage.setItem(`${this.tableName}_current_sort_state`, JSON.stringify(selectedView.sortState));
      }
      if (selectedView.groupingState) {
        localStorage.setItem(`${this.tableName}_current_grouping_state`, JSON.stringify(selectedView.groupingState));
      }
      if (selectedView.filtersState) {
        localStorage.setItem(`${this.tableName}_current_filters`, JSON.stringify(selectedView.filtersState));
      }

      // Reload the table to apply the new states
      if (this.genericTable) {
        // Force table to reload with new settings
        window.location.reload();
      }

      console.log(`Applied custom view "${viewName}" for ${this.tableName}`);
    }
  }
}
