import {
  Component,Input,Output,OnInit,OnDestroy,effect,ViewChild,ViewContainerRef,EventEmitter,Type,Injector,ChangeDetectorRef,HostListener,ElementRef,StaticProvider,
} from '@angular/core';

import { CommonModule, DatePipe } from '@angular/common';
import { CdkTableModule } from '@angular/cdk/table';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { MatBadgeModule } from '@angular/material/badge';
import { OverlayModule, Overlay, OverlayRef } from '@angular/cdk/overlay';
import { CdkPortalOutlet, ComponentPortal, ComponentType } from '@angular/cdk/portal';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';


// <-- Import your generic data source
import { GenericCDKDataSource } from '../../../core/datasources/generic-datasource';

import { GenericRequestService } from '../../../core/services/generic-request.service';
import { SnackNotifService     } from '../../../core/services/snack-notif.service';

// Your typical TableSettings classes/services
import { TableSettingsService } from '../../../core/services/table-settings.service';
import {
  TableSettings,TableSettingsFromDB,TableSettingsSearchObj,TableColumnSetting,CalcResult
} from '../../../core/models/table-settings';

// Model for grouping rows
import { GroupHeaderRow } from '../../../core/models/generic';
import { TableSettingsAdminFormComponent } from '../table-settings-admin-form/table-settings-admin-form.component';
import { SelectedMessageComponent } from '../inbox/selected-message/selected-message.component';
import { MessageService } from '../../../core/services/message.service';
import { MfgService } from '../../../core/services/request_mfg.service';
import { Observable, Subscription, take } from 'rxjs';
import { Tags } from '../../../core/models/tag';
import { SearchRelayService } from '../../../core/services/search-relay.service';
import { StaticTooltipComponent } from '../static-tooltip/static-tooltip.component';



export interface GenericListService<T, S extends Record<string, any> = Record<string, any>> {
  curList: T[];
  getAllRequests(searchParams?: S): void;
  // Optionally any other properties or methods needed...
}

//putting here intentionally. The model files are for basic interfaces.
export interface TagLoader extends GenericListService<Tags> {
  getTagsForJobAndCO(job:number, co:number): Observable<{ tags: Tags[] }>;
}

@Component({
  selector: 'app-generic-table',
  standalone: true,
  imports: [
    CommonModule,
    CdkTableModule,
    MatBadgeModule,
    DialogModule,
    OverlayModule,
    CdkPortalOutlet,
    SelectedMessageComponent,
    MatTooltipModule
  ],
  templateUrl: './generic-table.component.html',
  styleUrls: ['./generic-table.component.scss'],
  providers: [DatePipe]
})
export class GenericTableComponent<T extends { id: number; }> implements OnInit, OnDestroy {
  @ViewChild(CdkPortalOutlet, { static: true }) portalOutlet!: CdkPortalOutlet;

  private openTimer: any = null;
  private lastRefId: string | null = null;
  private mousePosition: { x: number; y: number } | null = null;
  private mouseMoved: boolean = false;

@HostListener('mouseover', ['$event'])
  handleMouseOver(ev: MouseEvent): void {
    const element = ev.target as HTMLElement;
    const refId = element?.dataset?.['ref'];

    if (refId && refId !== this.lastRefId) {
      this.lastRefId = refId;
      this.onRefHover(refId);                    // your relay
      if (this.openTimer) {
        clearTimeout(this.openTimer);
        this.openTimer = null;
      }

      // Initialize mouse position and movement tracking
      this.mousePosition = { x: ev.clientX, y: ev.clientY };
      this.mouseMoved = false;

      // Capture element position immediately for positioning
      const elementRect = element.getBoundingClientRect();

      this.openTimer = setTimeout(() => {
        console.log('Timer fired for refId:', refId, 'mouseMoved:', this.mouseMoved);
        // Only open if mouse hasn't moved significantly
        if (!this.mouseMoved) {
          const mockElement = {
            getBoundingClientRect: () => elementRect
          } as HTMLElement;
          this.searchRelay.emit(refId, mockElement, false);
          console.log('Opening tooltip for:', refId);
        } else if (this.mouseMoved) {
          console.log('NOT opening tooltip because mouse moved');
        }
      }, 500); // Longer delay to be less sensitive
    }
  }

  @HostListener('mousemove', ['$event'])
  handleMouseMove(ev: MouseEvent): void {
    // Track mouse movement to detect intentional hover
    if (this.mousePosition) {
      const deltaX = Math.abs(ev.clientX - this.mousePosition.x);
      const deltaY = Math.abs(ev.clientY - this.mousePosition.y);
      // If mouse moves more than 20 pixels, consider it movement
      if (deltaX > 20 || deltaY > 20) {
        this.mouseMoved = true;
        // Reset lastRefId when movement is detected so the same element can be hovered again
        if (this.openTimer) {
          clearTimeout(this.openTimer);
          this.openTimer = null;
          this.lastRefId = null; // Reset to allow new hover on the same element
          console.log('Timer cancelled due to mouse movement, lastRefId reset');
        }
      }
    }
  }

  @HostListener('mouseout', ['$event'])
  handleMouseOut(ev: MouseEvent): void {
    const element = ev.target as HTMLElement;
    const refId = element?.dataset?.['ref'];

    if (refId && refId === this.lastRefId) {
      // Cancel any pending timer when leaving the element
      if (this.openTimer) {
        clearTimeout(this.openTimer);
        this.openTimer = null;
        console.log('Timer cancelled due to mouseout for refId:', refId);
      }

      // Reset tracking variables
      this.lastRefId = null;
      this.mousePosition = null;
      this.mouseMoved = false;
    }
  }

  @HostListener('click', ['$event'])
  stopLinkNavigation(ev: MouseEvent): void {
    const el = ev.target as HTMLElement;
    if (el.dataset?.['ref']) {
      ev.preventDefault(); // Prevent link navigation
    }
  }

  /* ────────────────────────────────────────────────
     Ctrl/Cmd + Z  → undo
────────────────────────────────────────────────── */
  @HostListener('document:keydown', ['$event'])
  handleUndoKey(ev: KeyboardEvent) {
    if (!(ev.ctrlKey || ev.metaKey) || ev.key.toLowerCase() !== 'z') { return; }

    /* let the browser handle undo inside a raw <input> first */
    const tag = (ev.target as HTMLElement).tagName;
    if (tag === 'INPUT' || tag === 'TEXTAREA') { return; }

    ev.preventDefault();

    const last = this.undoStack.pop();
    if (!last) { return; }

    last.row[last.column] = last.prev;              // revert
    this.cdr.detectChanges();                       // refresh view
  }

  @ViewChild('table', { read: ElementRef }) table!: ElementRef<HTMLTableElement>;

  onEditorTab(ev: KeyboardEvent, row: T, col: keyof T | "actions") {
    ev.preventDefault();                    // stop the browser’s default tabbing
    const dir = ev.shiftKey ? -1 : 1;       // ⇧-Tab = backwards

    /* 1 ▸ commit the current cell */
    this.dataSource.stopEdit(row, col, true);

    /* 2 ▸ build a flat list of editable, *visible* columns (skip "actions") */
    const cols = this.visibleColumns
                  .filter(c => c !== 'actions')
                  .filter(c => this.isEditable(String(c))) as (keyof T)[];

    /* 3 ▸ find where we are and which column comes next */
    let colIdx = cols.indexOf(col as keyof T) + dir;
    let targetRow = row;

    /* wrap to the next / previous row when we run past the edge */
    if (colIdx < 0 || colIdx >= cols.length) {
      const rows = this.dataSource.data
                    .filter(r => this.isDataRow(r)) as T[];
      let rowIdx = rows.indexOf(row) + (dir > 0 ? 1 : -1);

      if (rowIdx < 0) rowIdx = rows.length - 1;
      if (rowIdx >= rows.length) rowIdx = 0;

      targetRow = rows[rowIdx];
      colIdx = dir > 0 ? 0 : cols.length - 1;
    }

    const nextCol = cols[colIdx];

    /* 4 ▸ focus the target cell – wait a tick so the view has refreshed */
    queueMicrotask(() => {
      const selector = `td[data-rowkey="${this.rowKeyFn(targetRow)}"][data-col="${String(nextCol)}"]`;
      const cell = this.table.nativeElement.querySelector<HTMLElement>(selector);
      cell?.focus();                        // triggers onCellFocus → startEdit
    });
  }

  private undoStack: { row: T; column: keyof T; prev: any }[] = [];
  public extraOptions: Record<string, Set<string>> = {};

  private overlayRef: OverlayRef | null = null;
  @Input() requestType = '';
  /** The name of the table (used for localStorage keys, etc.) */
  @Input() tableName = 'Generic Table';

  /** The array of columns that should appear, e.g. ['actions', 'id', 'name'] */
  @Input() displayedColumns: (keyof T & string | 'actions')[] = [];

  /** The actual service that fetches data and stores it in curList */
  @Input() dataService!: GenericListService<T>;

  @Input() editComponent!: ComponentType<any>;

  @Input() tagService?: TagLoader;

  /**  NEW: which component to show inside the expansion row  */
  @Input() detailComponent?: Type<any> | null = null;


  @Input() showDetailColumn = true;
  @Input() showInfoBtn = false;
  @Input() showPrintBtn = false;
  @Input() showEdit = true;

  @Input() searchParams: Record<string, any> | undefined;

  @Input() rowKeyFn: (row: any) => string = (row: any) => {
    // Try different ID fields based on request type
    return String(
      row?.requestID ??
      row?.projectmgntrecid ??
      row?.request_pk_id ??
      row?.request_sh_id ??
      row?.tag_id ??
      row?.id ??
      ''
    );
  };

  /** Let parent build *any* StaticProvider[] for the clicked row */
  @Input() extraProvidersFn: (row: T) => StaticProvider[] = () => [];

  /* private copy that survives setInterval() */
  private currentParams: Record<string, any> | undefined;

  /** Emits the full data array every time we (re)load it */
  @Output() dataLoaded = new EventEmitter<T[]>();

  /** Emits the list of saved views as soon as we fetch them */
  @Output() viewsLoaded = new EventEmitter<TableSettingsFromDB[]>(); // saved views,
  @Output() savedViewId = new EventEmitter<any>();

  // to get dynamic row count for example.
  @Output() visibleDataChange = new EventEmitter<Array<T | GroupHeaderRow<T>>>();

  private rowsSub?: Subscription;

  /** Convert anything to a trimmed, plain string */
  private sanit = (v: any): string => String(v ?? '').trim();

  /*** Internal state ***/
  data: T[] = [];

  // Once we filter out hidden columns, we store them here:
  visibleColumns: (keyof T | 'actions')[] = [];

  // The pinned columns
  frozenColumns: string[] = [];

  // Store original positions of columns before freezing
  private originalColumnPositions: Map<string, number> = new Map();

  // Store the original column order before any freeze operations
  private originalColumnOrder: (keyof T | 'actions')[] = [];

  // DataSource for the CDK table
  dataSource!: GenericCDKDataSource<T>;

  // For storing saved views from DB
  savedViews: TableSettingsFromDB[] = [];
  selectedViewId: number | undefined;

  // For dynamic "headerMapping"
  headerMapping: { [columnId: string]: string } = {};

  // For searching or retrieving table settings
  search_obj: TableSettingsSearchObj = {};

  // generic-table.component.ts
  /** which master rows are expanded – keyed by "job#-CO##" */
  private expandedKeys = new Set<string>();

  private isOpenedByClick: boolean = false;


  // Used for updating time, refresh intervals, etc.
  lastUpdatedTime: string = '';
  private refreshIntervalId: any;

  // Sorting icons
  checkmark = '✔';
  sortDesc = 'M12 19V5m0 14-4-4m4 4 4-4';
  sortAsc  = 'M12 6v13m0-13 4 4m-4-4-4 4';
  tagsDisplayedColumns!: string[];

  constructor(
    private tableSettingsService: TableSettingsService,
    private dialog: Dialog,
    private overlay: Overlay,
    private datePipe: DatePipe,
    private viewContainerRef: ViewContainerRef,
    private messageService : MessageService,
    public searchRelay: SearchRelayService,
    private sanitizer: DomSanitizer,
    private genReqSer: GenericRequestService,
    private snackNotifService: SnackNotifService,
    private cdr: ChangeDetectorRef
  ) {
    // React to changes in table settings
    effect(() => {
      const settings = this.tableSettingsService.tableSettingsSignal();
      if (settings && settings.settings?.length) {
        // Build the header mapping
        const mapping: { [key: string]: string } = {};
        settings.settings.forEach(setting => {
          mapping[setting.field_name] = setting.field_name_display;
        });
        this.headerMapping = mapping;

        // Filter the columns to hide or show - PRESERVE the order from displayedColumns
        const filteredColumns = this.displayedColumns.filter(column => {
          if (column === 'actions') return true;
          // Find setting for this column
          const columnSetting = settings.settings.find(s => s.field_name === column);
          return columnSetting && columnSetting.field_hidden === 'no';
        });

        // Only update visibleColumns if the filtered result is different
        // This preserves the order from restoreColumnOrder()
        if (JSON.stringify(this.visibleColumns) !== JSON.stringify(filteredColumns)) {
          this.visibleColumns = filteredColumns;
        }
      } else {
        // If no settings exist, show all
        this.visibleColumns = this.displayedColumns;
      }
      this.compileCalcsFromSettings();
    });

    // React to changes in the service's data
    effect(() => {
      const currentList = this.dataService?.curList;
      if (currentList && currentList.length) {
        this.loadData();
      }
    });

    // Listen for popup close to reset click state
    this.searchRelay.popupClosed.subscribe(() => {
      this.isOpenedByClick = false;
      this.lastRefId = null; // Reset lastRefId so the same element can be hovered again
    });
  } // end of constructor


  ngOnInit(): void {
    // Let the service fetch data
    console.log('this.dataService from inside ngOnInit() of generic table: ', this.dataService)
    this.currentParams = this.searchParams;
    this.dataService.getAllRequests(this.currentParams);


    // Try to restore a saved view ID
    console.log("Current table is: ", this.tableName)
    const storedId = localStorage.getItem(`${this.tableName}_current_settings_id`);
    if (storedId) {
      this.selectedViewId = parseInt(storedId, 10);
      console.log('this.selectedViewId from generic table: ', this.selectedViewId)
      this.savedViewId.emit(this.selectedViewId);
    }

    // Attempt to restore pinned columns
    const savedFrozen = localStorage.getItem(`${this.tableName}_current_frozen_columns`);
    if (savedFrozen) {
      this.frozenColumns = JSON.parse(savedFrozen);
    }

    this.expandedKeys = new Set(
      JSON.parse(localStorage.getItem(`${this.tableName}_expanded`) ?? '[]')
    );

    // Restore column order (for freeze positioning)
    this.restoreColumnOrder();

    this.getSavedSettings(this.tableName);

    this.lastUpdatedTime = this.datePipe.transform(new Date(), 'h:mm:ss a') ?? '';

    // Set up an auto-refresh interval (example: 5 min)
    this.refreshIntervalId = setInterval(() => {
      this.dataService.getAllRequests(this.currentParams);
      this.loadData();
      this.lastUpdatedTime = this.datePipe.transform(new Date(), 'h:mm:ss a') ?? '';
      console.log('Refreshed data at', this.lastUpdatedTime);
    }, 5 * 60 * 1000);
  }

  ngOnDestroy(): void {
    if (this.refreshIntervalId) {
      clearInterval(this.refreshIntervalId);
    }
  }

   /** Load data from the service into the dataSource. Also restore sort/filter states. */
  loadData(): void {
    const rawArray = this.dataService.curList;
    console.log('loadData() found array: ', rawArray);
    const withStatus = rawArray.map(r => ({ ...r, Status: this.statusFromRow(r) }));
    this.dataSource = new GenericCDKDataSource<T>(withStatus as any,
      this.genReqSer,
      this.snackNotifService,
      this.tableName);


    // Build the data source with the new data
    this.dataSource = new GenericCDKDataSource<T>(rawArray,
      this.genReqSer,
      this.snackNotifService,
      this.tableName);

    // Attempt to restore sort settings
    const savedSortState = localStorage.getItem(`${this.tableName}_current_sort_state`);
    if (savedSortState) {
      const parsed = JSON.parse(savedSortState);
      this.dataSource._sortState.column = parsed.column;
      this.dataSource._sortState.direction = parsed.direction;
    }

    // Attempt to restore filters
    const savedFilters = localStorage.getItem(`${this.tableName}_current_filters`);
    if (savedFilters) {
      const parsedFilters: { [col: string]: string[] } = JSON.parse(savedFilters);
      this.dataSource.restoreActiveFilters(parsedFilters);
    }

    // Attempt to restore group-by
    const savedGroupBy = localStorage.getItem(`${this.tableName}_current_grouping_state`);
    if (savedGroupBy) {
      // Must match a key in T, so be sure your service sets it up
      this.dataSource.setGroupBy(savedGroupBy as keyof T);
    }

    // Attempt to restore frozen columns (like other states)
    const savedFrozen = localStorage.getItem(`${this.tableName}_current_frozen_columns`);
    if (savedFrozen) {
      try {
        this.frozenColumns = JSON.parse(savedFrozen);
        console.log('🧊 Restored frozen columns in loadData():', this.frozenColumns);
        // When loading from saved state, we don't have original positions stored
        // So we clear the map and original order to avoid conflicts
        this.originalColumnPositions.clear();
        this.originalColumnOrder = [];
        // Reposition columns according to frozen state
        this.repositionAllColumns();
      } catch (error) {
        console.error('Error parsing frozen columns in loadData():', error);
        this.frozenColumns = [];
      }
    } else {
      this.frozenColumns = [];
      this.originalColumnPositions.clear();
      this.originalColumnOrder = [];
      // Reposition columns to unfrozen state
      this.repositionAllColumns();
    }
    /* --------- notify parent that fresh data is ready --------- */
    this.dataLoaded.emit(rawArray);

     /* ── push every change downstream ── */
    this.rowsSub?.unsubscribe();             // tidy any previous sub
    this.rowsSub = this.dataSource           //     the datasource itself
      .connect()                             // (an Observable<Array<…>>)
      .subscribe(viewRows => {
        this.visibleDataChange.emit(viewRows);
      });
      /*   restore panes after *every* refresh  */
      /*   prevents recursive signal updates */
      queueMicrotask(() => this.rebuildExpandedRows()); //https://developer.mozilla.org/en-US/docs/Web/API/Window/queueMicrotask
  }


  /* ------------ STATUS helpers ------------ */

  private readStatus = (r: any): string =>
  r?.request_sh_status   // ① Shipping requests
  ?? r?.request_pk_status
  ?? r?.status           // ② generic lower-case
  ?? r?.Status           // ③ original Title-case
  ?? r?.r_status         // ④ legacy “r_” field
  ?? '';                 // ⑤ nothing found

  statusFromRow(r: any): string {
    if (r.total_tags === 0){
      r.r_status = 'Pending'
      return 'Pending';
    }
    if (r.total_tags >= 1 && r.active_tags >= 1)
      {
      r.r_status = 'Active'
      return 'Active'; }
    if (r.total_tags >= 1 &&
        (!r.active_tags || r.active_tags === 0) &&
        (r.r_status ?? '').trim().toLowerCase() === 'n/a')
      {
        r.r_status = 'In QC'
        return 'In QC';
      }
    


    // Manual states already stored on the record
    return this.readStatus(r);      // keep whatever author stored
  }

  private readonly statusColours: Record<string,string> = {
    'Pending'           : '#FFF1CE',
    'In QC'             : '#ffff99',
    'QC'                : '#ffff99',
    'Large Format'      : '#ffccff',
    'Outside Finishing' : '#ffcc66',
    'Ready for packout' : '#ff6699',
    'QC Kickback'       : '#c9f2fc',
    'Completed'         : '#c9fcea',
    'complete'          : '#c9fcea',
    // 'Active' –- no colour
  };


  private persistExpanded() {
    localStorage.setItem(
      `${this.tableName}_expanded`,
      JSON.stringify([...this.expandedKeys])
    );
  }


  /* things for calculations and formatting:
   ----------------------------------------------------------
    Build { columnId → compiledFn } once per view */

  private compiledCalcs: Record<string,(row:any)=>CalcResult> = {};

/** compile every field_calc expression in the current view */
private compileCalcsFromSettings() {
  this.compiledCalcs = {};                    // wipe old cache

  const settings = this.tableSettingsService.tableSettingsSignal();
  (settings?.settings ?? []).forEach(s => {
    if (s.field_calc?.trim()) {
      console.log('⎆ compiling calc for column', s.field_name);
      const normalised = s.field_calc.replace(/\s*\|\s*/g, '|').replace(/\s*:\s*/g, ':');
      this.compiledCalcs[s.field_name] = this.makeCalcFn(normalised);
    }
  });
}

/* ----------------------------------------------------------
   Helper that turns a template string into a function(row)
---------------------------------------------------------- */

/** compile one template → a fast function(row) */
private makeCalcFn(tmpl: string): (row: any) => CalcResult {

  const tokenRE =
    /{{\s*([a-zA-Z0-9_]+)(?:\s*\|\s*([a-zA-Z0-9_]+)(?:\s*:\s*([a-zA-Z0-9_]+))?)?\s*}}/g;

  return (row: any) => {
    let style: { [k: string]: any } | undefined;
    let html:  SafeHtml | undefined;             // ① keep richest fragment

    const text = tmpl.replace(tokenRE, (_, fld, pipe, arg) => {
      const raw = row?.[fld];

      if (pipe) {
        /* --- evaluate the pipe --- */
        const out = this.pipe(
          pipe,
          raw,
          arg !== undefined ? row?.[String(arg).trim()] ?? arg : undefined
        );

        if (out && typeof out === 'object' && 'text' in out) {
          /* keep style & html when provided */
          if (out.style) style = { ...(style ?? {}), ...out.style };
          if (out.html ) html  = out.html;
          return out.text;                       // replaces the token
        }
        return String(out ?? '');
      }

      /* plain {{ field }} */
      return String(raw ?? '');
    });

    /* ②  〈text, style, html〉 back to the table */
    return { text, style, html } as CalcResult & { html?: SafeHtml };
  };
}

/** Return SafeHtml if the calc produced raw HTML, else undefined */
getRichHtml(row: any, column: string): SafeHtml | undefined {
  const fn = this.compiledCalcs[column];
  const res = fn ? fn(row) : undefined;
  return res?.html;               // undefined when pipe didn't set html
}

/* ----------------------------------------------------------
   Library of "pipes"
---------------------------------------------------------- */
private pipe(name: string, value: any, extra?: any): any {

  switch (name) {

    case 'json_ref_id': {
        /**
         *  Accepts:
         *    – a single object  { reference_id, reference_type }
         *    – a JSON string    '[{"reference_id":"…"}, …]'
         *    – an array of either form
         *  Returns a string with **only** the `reference_id` values, one per line.
         */
        if (value == null || value === '') return '';

        const normalise = (v: any): any => {
          if (typeof v === 'string') {
            try { return JSON.parse(v); } catch { return v; }
          }
          return v;
        };

        const extract = (obj: any): string => (obj && obj.reference_id) ? obj.reference_id : '';

        if (Array.isArray(value)) {
          const refs = value.map(normalise).map(extract).filter(Boolean);
          return refs.join('\n'); // newline‑separated
        }

        const obj = normalise(value);
        return extract(obj);
      }
    /* ------------------------------------------------------------
   {{ something | refs }}
   • understands the same JSON formats as json_ref_id
   • still works with a plain string full of IDs
    ------------------------------------------------------------ */
    case 'refs': {
      if (value == null || value === '') {
        return { text: '' };
      }

      /* helper: JSON-decode strings once */
      const normalise = (v: any): any => {
        if (typeof v === 'string') {
          try { return JSON.parse(v); } catch { return v; }
        }
        return v;
      };

      /* helper: get reference_id from an object */
      const extract = (obj: any): string =>
        (obj && obj.reference_id) ? String(obj.reference_id) : '';

      /* 1 ▸ try the "structured" path first ----------------------------- */
      let ids: string[] = [];

      if (Array.isArray(value)) {
        ids = value.map(normalise).map(extract).filter(Boolean);
      } else {
        const obj = normalise(value);
        if (typeof obj === 'object') {
          const id = extract(obj);
          if (id) { ids = [id]; }            // found one ⇒ keep
          else    { return { text: '' }; }   // empty object ⇒ **stop here**
        }
      }

      /* 2 ▸ fall back to "plain text" (newline / comma / semicolon) ----- */
      if (!ids.length) {
        ids = String(value).split(/\s*[\n,;]\s*/).filter(Boolean);
      }

      if (!ids.length) {
        return { text: '' };          // still nothing → blank cell
      }

      /* 3 ▸ build the link HTML ---------------------------------------- */
      const html = ids.map(id =>
        `<a href="#" data-ref="${id}"
            class="text-blue-600 underline hover:text-blue-800 block leading-2 pb-2">
          ${id}
        </a>`).join('');

      return {
        text : '',   // ignored because the template renders .html first
        html : this.sanitizer.bypassSecurityTrustHtml(html)
      };
    }
    case 'json_req_status': {
        // Accept stringified JSON or array of objects
        if (value == null || value === '') return '';
        let arr: any[];
        try {
          arr = Array.isArray(value) ? value : JSON.parse(value);
        } catch {
          return '';
        }

        const withID = arr.filter(o => o && o.requestID);
        const count  = withID.length;
        if (!count) return '0';

        const mapStatus = (s: any) => (String(s).trim().toLowerCase() === 'n/a' ? 'Active' : String(s).trim());
        const statuses  = Array.from(new Set(withID.map(o => mapStatus(o.r_status))));
        return statuses.length === 1 ? `${count} ${statuses[0]}` : `${count} Mixed`;
      }
    case 'addDays': {
      if (value == null || extra == null) return value;
      const days = Number(extra);
      //console.log("DAYS: (extra): ", days)
      if (isNaN(days)) return value;

      /** Robust local‑time parser that also accepts plain YYYY‑MM‑DD */
      const toDate = (v: any): Date | null => {
        if (v instanceof Date) return new Date(v.getTime());
        if (typeof v === 'string') {
          // 1. Bare date → YYYY‑MM‑DD
          const m = v.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/);
          if (m) {
            const [, yy, mm, dd] = m;
            return new Date(+yy, +mm - 1, +dd); // local midnight
          }
          // 2. Fallback to JS Date parser for full ISO strings
          const d = new Date(v);
          return isNaN(d.getTime()) ? null : d;
        }
        return null;
      };

      const d = toDate(value);
      if (!d) return value;
      d.setDate(d.getDate() + days);
      return this.datePipe.transform(d, 'yyyy-MM-dd') ?? value;
    }

    case 'plural_hr': {
      if (value === null || value === undefined || value === '') {
        return '';
      }

      const n = Number(value);

      if (isNaN(n) || n === 1 || !Number.isInteger(n)) {
        return 'hr';                         // no "s"
      }

      return 'hrs';
    }

    case 'percentOf':
      if (value == null || extra == null) return '';
      const pct = (Number(value) / Number(extra)) * 100;
      return pct.toFixed(0);                        // "75"

    case 'redIfBeforeToday': {
      if (value == null) { return ''; }

      /* 1 ▸ grab just the YYYY-MM-DD part */
      const isoDate = String(value).slice(0, 10);          // "2025-05-08"
      if (!/^\d{4}-\d{2}-\d{2}$/.test(isoDate)) {          // not a date → bail
        return value;
      }

      /* 2 ▸ build a date in *local* time at midnight */
      const d = new Date(`${isoDate}T00:00:00`);

      /* 3 ▸ today at 00:00:00 local */
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const isPast = d < today;

      return {
        text : this.datePipe.transform(d, 'MM-dd-yyyy')!,  // 05-08-2025
        style: isPast ? { color: '#d8000c' } : undefined   // paint red if late
      };
    }

    // ➜ add more pipes here
  }

  return value;            // unknown pipe → no change
}


reloadAfterDialog(): void {
    /*  ⬆︎ once curList changes, the existing `effect()` → loadData() runs   */
    this.dataService.getAllRequests(this.currentParams);
  }


  isEditable(col: string): boolean {
    return this.getColumnSetting(col)?.field_editable === 'yes';
  }

  /* ────────────────────────────────────────────────
     focus → start edit
  ────────────────────────────────────────────────── */
  onCellFocus(row: T, col: keyof T | 'actions'): void {
  if (col === 'actions') { return; }       // guard – never edit the actions column
  this.dataSource.startEdit(row, col as keyof T);

  // Force change detection and then focus the editor
  this.cdr.detectChanges();
  setTimeout(() => {
    this.focusEditor(row, col as keyof T);
  }, 0);
}

  /** Focuses the cell (not the editor) so onCellFocus() will reopen edit-mode. */
  private focusCell(targetRow: T, col: keyof T) {
    queueMicrotask(() => {                           // wait for view refresh
      const sel =
        `td[data-rowkey="${this.rowKeyFn(targetRow)}"][data-col="${String(col)}"]`;
      const cell = document.querySelector<HTMLElement>(sel);
      cell?.focus();                                 // triggers onCellFocus
    });
  }

  /* ───────── Arrow-key handler ───────── */

  /**
   * @param ev   the KeyboardEvent
   * @param row  current data row
   * @param col  current column
   * @param dir  -1 for Arrow-Up,  +1 for Arrow-Down
   */
  onEditorArrow(
    ev: KeyboardEvent,
    row: T,
    col: keyof T | "actions",
    dir: -1 | 1
  ): void {

    /* let Ctrl/Cmd+↑/↓ inside a text box move the caret normally */
    if (ev.ctrlKey || ev.metaKey) { return; }

    ev.preventDefault();                 // stop the default caret movement

    /* 1 ▸ commit current edit */
    this.dataSource.stopEdit(row, col, true);

    /* 2 ▸ flat list of *visible* data rows (skip group & detail rows) */
    const rows = this.dataSource.data.filter(r => this.isDataRow(r)) as T[];

    /* 3 ▸ compute the next row index, with wrap-around */
    let idx = rows.indexOf(row) + dir;
    if (idx < 0) idx = rows.length - 1;
    if (idx >= rows.length) idx = 0;

    /* 4 ▸ move focus to the target cell */
    this.focusCell(rows[idx], col as keyof T);
  }

  /* ────────────────────────────────────────────────
   remember *previous* value before we mutate
  ────────────────────────────────────────────────── */
  updateField(evt: Event, row: T, column: keyof T) {
    const prev = row[column];
    const val  = (evt.target as HTMLInputElement).value;

    /* push onto undo stack */
    this.undoStack.push({ row, column, prev });

    /* if this is a “select” column, remember never-seen values */
    if (this.getColumnSetting(String(column))?.field_editable_type === 'select') {
      const set = this.extraOptions[column as string] ??= new Set<string>();
      set.add(val);
    }

    /* original body of updateField … */
    this.dataSource.updateField(evt, row, column);
  }


  /** Load all saved table settings from the server, then decide which one to apply.
   *
   * Decision matrix
   * ────────────────
   *  1. no localStorage entry            → store & apply the “Default” view
   *  2. stored ID === “Default” ID       → keep storage, (re-)apply “Default”
   *  3. stored ID !== “Default” ID       → apply the view whose ID is stored
   */
  getSavedSettings(tableName: string): void {
    this.search_obj.table_name = tableName;
    const storageKey = `${tableName}_current_settings_id`;
    console.log('*********** From inside getSavedSettings(tableName) **************************')
    console.log('Table name: ', tableName)
    this.tableSettingsService.getServerSettings(this.search_obj).subscribe({
      next: resp => {
        /* 0 ─────────────────────────────────────────────────────────── */
        /* turn resp.body into a clean array                                   */
        const allViews: TableSettingsFromDB[] =
          Array.isArray(resp.body) ? resp.body : [];

        /* keep everything (so callers can populate a <select>)                */
        this.savedViews = allViews;
        this.viewsLoaded.emit(this.savedViews);

        /* 1 ─────────────────────────────────────────────────────────── */
        /* restrict to views **for this table**                                */
        const tableViews = allViews.filter(
          v => v.template_name === tableName
        );

        /* find the row that is literally called “Default”                     */
        const defaultView = tableViews.find(
          v => v.settings_name?.trim().toLowerCase() === 'default'
        );
        const defaultId = defaultView?.settings_id ?? null;
        console.log('defaultId: ', defaultId)
        /* 2 ─────────────────────────────────────────────────────────── */
        /* what did we store during the last session (if anything)?            */
        const storedIdStr = localStorage.getItem(storageKey);
        const storedId    = storedIdStr ? parseInt(storedIdStr, 10) : null;

        let activeView: TableSettingsFromDB | undefined;

        /* CASE 1 – first visit: nothing stored → apply “Default”         */
        if (storedId === null && defaultView) {
          localStorage.setItem(storageKey, String(defaultId));
          activeView = defaultView;
          console.log('got to CASE 1')
        }
        /* CASE 2 – storage already points at the Default view            */
        else if (storedId !== null && storedId === defaultId) {
          activeView = defaultView;
          console.log('got to CASE 2')
        }
        /* CASE 3 – storage points at some *other* saved view             */
        else if (storedId !== null) {
          activeView = tableViews.find(v => v.settings_id === storedId);
          console.log('got to CASE 3')
        }

        /* keep UI widgets (e.g. <select>) in sync                            */
        this.selectedViewId = activeView?.settings_id;

        /* 3 ─────────────────────────────────────────────────────────── */
        /* push the chosen layout into the reactive signal                   */
        if (activeView) {
          const tableSettings: TableSettings = {
            tableName,
            settings: activeView.settings ?? []
          };
          this.tableSettingsService.setServerSettings(tableSettings);
        } else {
          console.warn(
            `[GenericTable] No matching view found for “${tableName}”. ` +
            'Showing all columns as a fallback.'
          );
        }
      console.log('*********** end of from inside getSavedSettings(tableName) **************************')
      },
      error: err => console.error('Error retrieving table settings:', err)
    });
  }

  /** Determine if the row is a group header or a data row. */
  isGroupRow = (
    index: number,
    row: T | GroupHeaderRow<T>
  ) => (row as GroupHeaderRow<T>).__group === true;


  isDataRow(row: T | GroupHeaderRow<T> | { __detail?: true }): row is T {
    return !(row as any).__group && !(row as any).__detail;
  }

  private focusEditor(row: T, col: keyof T, retryCount = 0) {
  const selector =
    `td[data-rowkey="${this.rowKeyFn(row)}"][data-col="${String(col)}"]
     input, td[data-rowkey="${this.rowKeyFn(row)}"][data-col="${String(col)}"]
     select`;
  const editor = document.querySelector<HTMLElement>(selector);

  if (editor) {
    editor.focus();
    if (editor instanceof HTMLInputElement) { editor.select(); } // highlight
  } else if (retryCount < 3) {
    // Retry up to 3 times with increasing delays
    setTimeout(() => {
      this.focusEditor(row, col, retryCount + 1);
    }, 10 * (retryCount + 1)); // 10ms, 20ms, 30ms
  }
}

  // isDataRow = (
  //   index: number,
  //   row: T | GroupHeaderRow<T>
  // ) => !(row as GroupHeaderRow<T>).__group;

  /** Format a value for display based on column settings. */
  /* pass the whole row, not only the value */
  formatCell(row: any, columnKey: string ): string {
    /** ① run a field-calc if one was defined for this column */
    const fn = this.compiledCalcs[columnKey as string];
    if (fn) {
      return fn(row).text;
    }

    /** ② fallback: existing date-format logic etc. */
    const value   = row?.[columnKey];
    const setting = this.getColumnSetting(columnKey as string);

    if (setting?.field_is_date === 'yes' && typeof value === 'string') {
      return this._formatDateLocal(value);
    }

    if (setting?.field_is_date_time === 'yes' && typeof value === 'string') {
     return this._formatDateTimeLocal(value);
 }

    return value ?? '';
  }


  /** Things for MFG requests: the natural primary-key for a request = Job + CO */
  public reqKey(r: any): string {
    const job = this.sanit(r.job_number);
    const co  = this.sanit(r.chg_order).padStart(3, '0');
    return `${job}-CO${co}`;
  }

  private key = (row: any) => this.rowKeyFn(row);

  /** true when <tr> is the visible master row (not group-row) */
  //isMasterRow = (_: number, row: any) => !row.__detail;

  /** true for the synthetic detail row we insert right after the master */
  //isDetailRow = (_: number, row: any) => row.__detail === true;

  /** was this master row expanded last time? */
  isExpanded(row: any) { return this.expandedKeys.has(this.reqKey(row)); }

  /** Convert any stored date to the yyyy‑MM‑dd string a <input type=date> expects. */
  getDateInputValue(value: any): string {
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
      return value.slice(0, 10);          // yyyy-MM-dd exactly as stored
    }
    // Fallback for genuine Date objects (unlikely)
    const d = new Date(value);
    return !isNaN(d.getTime()) ? d.toISOString().substring(0, 10) : '';
  }

  hasTooltip(column: string): boolean {
    const s = this.getColumnSetting(column);
    return s?.field_has_tooltip === 'yes';
  }

  _formatDateLocal(iso: string): string {
    // Works for 'YYYY-MM-DD' and 'YYYY-MM-DDTHH:mm:ss'
    const parts = iso.slice(0, 10).split('-');   // [yyyy, mm, dd]
    if (parts.length !== 3) { return iso; }      // guard – not a date
    const [yy, mm, dd] = parts;
    return `${mm}-${dd}-${yy}`;                  // MM-DD-YYY
  }


   _formatDateTimeLocal(iso: string): string {
    const d = new Date(iso);          // parsed in local TZ
    return this.datePipe.transform(d, 'yyyy-MM-dd HH:mm:ss') ?? iso;
  }

  /** For pinned columns. */
  isColumnFrozen(column: string): boolean {
    return this.frozenColumns.includes(column);
  }

  expandedMap = new Map<T, Injector>();   // row → injector

  /** rowKey → injector */
  expandedInjectors = new Map<string, Injector>();

  /** *master* data row (the one with cells you edit & click) */
  isMasterRow = (_: number, row: any) =>
    !row.__detail && !this.isGroupRow(0, row);     // ✦ exclude detail objects

  /** detail‑placeholder row we inserted programmatically */
  isDetailRow = (_: number, row: any) => row.__detail === true;

  private buildInjector(master: T): Injector {
    return Injector.create({
      providers: [
        ...this.extraProvidersFn(master),          // ← whatever the host returns
        { provide: 'ROW', useValue: master }       // keep a sane default
      ],
      parent: this.viewContainerRef.injector
    });
  }

  /** Expand / collapse a master row in-place. */
  toggleExpand(master: T): void {
    const rowKey = this.key(master);   // e.g. "12345‑CO007"
    console.log('rowKey:', rowKey)

    /* ─ collapse ─ */
    if (this.expandedKeys.has(rowKey)) {
      this.expandedKeys.delete(rowKey);
      this.dataSource.removeDetailRowAfter(master);
      this.expandedInjectors.delete(rowKey);   // ← remove its injector
      this.persistExpanded();
      return;
    }

    /* ─ expand ─ */

    const injector  = this.buildInjector(master);

    const detailRow: any = { __detail: true, _rowKey: rowKey };
    this.dataSource.insertDetailRowAfter(master, detailRow);

    this.expandedInjectors.set(rowKey, injector);   // ← store with stable key
    this.expandedKeys.add(rowKey);
    this.persistExpanded();
    console.log('@#$@#$@# detailRow: ', detailRow)
    console.log('@#$@#$@# @#$@#$@#@#$@#$@#@#$@#$@#@#$@#$@#@#$@#$@# injector: ', injector)
    console.log("expandedInjectors.get(reqKey(master)): ", this.expandedInjectors.get(this.reqKey(master)))
    console.log(this.expandedInjectors)
  }

  /** Build 〈key → injector〉 again after every fresh data load */
  private rebuilding = false;

  private rebuildExpandedRows(): void {
    if (this.rebuilding) { return; }
    this.rebuilding = true;


    /* wipe leftovers, then recreate the panes */

    this.expandedInjectors.clear();

    this.dataSource.data
        .filter(r => this.isDataRow(r))            // 🔸 keeps only real rows
        .forEach(master => {
          const key = this.key(master);            // master is now T
          if (this.expandedKeys.has(key)) {
            this.createDetailRow(master, key);
          }
        });

    this.rebuilding = false;
  }

  /** Inserts a detail row + injector *without* touching expandedKeys */
  private createDetailRow(master: T, key: string): void {
    /* avoid duplicates when you refresh very fast */
    if (this.expandedInjectors.has(key)) { return; }

    const injector = this.buildInjector(master);

    const detailRow: any = { __detail: true, _rowKey: key };
    this.dataSource.insertDetailRowAfter(master, detailRow);

    this.expandedInjectors.set(key, injector);
  }

  getRefIds(raw: string | null | undefined): string[] {
    if (!raw) { return []; }
    return String(raw)
            .split(/\s*[\n,;]\s*/)      // newline, comma, semicolon…
            .filter(id => id.trim());
  }

    /* ---  relay the hover event ------- */
  onRefHover(id: string) {
    this.searchRelay.emit(id);           // just hand it to the service
  }


  toggleColumnFreeze(column: string): void {
    if (column === 'actions') return;

    const i = this.frozenColumns.indexOf(column);
    const wasFrozen = i >= 0;

    if (wasFrozen) {
      this.frozenColumns.splice(i, 1);
      // Move column back to its original position
      this.moveColumnToOriginalPosition(column);
    } else {
      // Store original position before freezing
      const currentIndex = this.displayedColumns.indexOf(column as any);
      this.originalColumnPositions.set(column, currentIndex);
      console.log(`🧊 Storing original position for "${column}": ${currentIndex}`);

      // Store the original order if this is the first freeze operation
      if (this.originalColumnOrder.length === 0) {
        this.originalColumnOrder = [...this.displayedColumns];
        console.log(`📋 Storing original column order:`, this.originalColumnOrder.map(c => c.toString()));
      }

      this.frozenColumns.push(column);
      // Move the frozen column to the left (after actions column)
      this.moveColumnToFrozenPosition(column);
    }
    localStorage.setItem(`${this.tableName}_current_frozen_columns`, JSON.stringify(this.frozenColumns));

    // Save the new column order
    localStorage.setItem(`${this.tableName}_column_order`, JSON.stringify(this.displayedColumns));
  }









  /**
   * Restores the column order from localStorage (for freeze positioning)
   */
  private restoreColumnOrder(): void {
    const savedOrder = localStorage.getItem(`${this.tableName}_column_order`);
    if (savedOrder) {
      try {
        const parsedOrder = JSON.parse(savedOrder);
        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {
          // Validate that all columns in the saved order still exist in displayedColumns
          const validColumns = parsedOrder.filter(col => this.displayedColumns.includes(col as any));

          // Add any new columns that weren't in the saved order
          const newColumns = this.displayedColumns.filter(col => !validColumns.includes(col as any));

          // Combine: saved order + new columns
          this.displayedColumns = [...validColumns, ...newColumns] as (keyof T & string | 'actions')[];
          this.visibleColumns = [...this.displayedColumns]; // Force sync with restored order
        }
      } catch (error) {
        console.error('Error parsing saved column order:', error);
      }
    }
  }

  /**
   * Moves a column to the frozen position (right after the actions column)
   */
  private moveColumnToFrozenPosition(column: string): void {
    // Find current position of the column
    const currentIndex = this.displayedColumns.indexOf(column as any);
    if (currentIndex === -1) return;

    // Remove the column from its current position
    const newOrder = [...this.displayedColumns];
    newOrder.splice(currentIndex, 1);

    // Find the position to insert (after actions and other already frozen columns)
    const actionsIndex = newOrder.indexOf('actions' as any);
    // Count how many frozen columns are already positioned (excluding the current one)
    const alreadyFrozenCount = this.frozenColumns.filter(col => col !== column).length;
    const insertPosition = actionsIndex + 1 + alreadyFrozenCount;

    // Insert the column at the frozen position
    newOrder.splice(insertPosition, 0, column as any);

    // Update the arrays
    this.displayedColumns = newOrder;
    this.visibleColumns = newOrder;

    // Save the new column order to localStorage
    localStorage.setItem(`${this.tableName}_column_order`, JSON.stringify(newOrder));
  }

  /**
   * Moves a column back to its original position (before it was frozen)
   */
  private moveColumnToOriginalPosition(column: string): void {
    const currentIndex = this.displayedColumns.indexOf(column as any);
    if (currentIndex === -1) {
      console.warn(`Column "${column}" not found in displayedColumns`);
      return;
    }

    // Get the stored original position
    const storedOriginalPosition = this.originalColumnPositions.get(column);
    if (storedOriginalPosition === undefined) {
      console.warn(`No original position stored for column "${column}", placing after frozen columns`);
      // Fallback to old behavior: place after frozen columns
      this.moveColumnToUnfrozenPosition(column);
      return;
    }

    console.log(`🔄 Moving "${column}" back to stored original position ${storedOriginalPosition}`);

    // IMPORTANT: Use the stored original position, not the current position in displayedColumns
    // because displayedColumns has been modified by previous freeze/unfreeze operations
    const originalPosition = storedOriginalPosition;

    // Remove the column from its current position
    const newOrder = [...this.displayedColumns];
    newOrder.splice(currentIndex, 1);

    // Calculate the adjusted position accounting for currently frozen columns
    console.log(`🔄 Stored original position of "${column}":`, originalPosition);
    console.log(`🔄 Current order before unfreeze:`, newOrder.map(c => c.toString()));
    console.log(`🔄 Currently frozen columns:`, this.frozenColumns);

    // Strategy: Use the stored original order to count unfrozen columns before our column
    let unfrozenColumnsBefore = 0;

    if (this.originalColumnOrder.length > 0) {
      console.log(`📋 Using stored original order:`, this.originalColumnOrder.map(c => c.toString()));

      // Count unfrozen columns that were before our column in the original order
      for (let i = 0; i < originalPosition && i < this.originalColumnOrder.length; i++) {
        const col = this.originalColumnOrder[i].toString();
        if (col === 'actions') continue;

        // Check if this column is currently unfrozen and still exists in newOrder
        if (!this.frozenColumns.includes(col) && newOrder.some(c => c.toString() === col)) {
          unfrozenColumnsBefore++;
          console.log(`✅ Counting "${col}" as unfrozen before (total: ${unfrozenColumnsBefore})`);
        }
      }
    } else {
      console.warn(`⚠️ No original order stored, using fallback method`);
      // Fallback to the old method if no original order is stored
      this.moveColumnToUnfrozenPosition(column);
      return;
    }

    console.log(`🔄 Unfrozen columns that should be before "${column}": ${unfrozenColumnsBefore}`);

    // Calculate final position: 1 (actions) + frozen count + unfrozen columns before
    const frozenCount = this.frozenColumns.length;
    const adjustedPosition = 1 + frozenCount + unfrozenColumnsBefore;

    console.log(`🔄 Final position calculation: 1 (actions) + ${frozenCount} (frozen) + ${unfrozenColumnsBefore} (unfrozen before) = ${adjustedPosition}`);

    // Make sure we don't go beyond the array bounds
    const finalPosition = Math.min(adjustedPosition, newOrder.length);

    // Insert the column at the final position
    console.log(`🎯 Inserting "${column}" at position ${finalPosition} in:`, newOrder.map(c => c.toString()));
    newOrder.splice(finalPosition, 0, column as any);
    console.log(`✅ Final order:`, newOrder.map(c => c.toString()));

    // Update the arrays
    this.displayedColumns = newOrder;
    this.visibleColumns = newOrder;

    // Clean up the stored position
    this.originalColumnPositions.delete(column);

    // If no more columns are frozen, clear the original order
    if (this.frozenColumns.length === 0) {
      this.originalColumnOrder = [];
      console.log(`🧹 Cleared original column order (no more frozen columns)`);
    }

    // Save the new column order to localStorage
    localStorage.setItem(`${this.tableName}_column_order`, JSON.stringify(newOrder));
  }

  /**
   * Fallback method: Moves a column to unfrozen position (after frozen columns)
   */
  private moveColumnToUnfrozenPosition(column: string): void {
    const currentIndex = this.displayedColumns.indexOf(column as any);
    if (currentIndex === -1) return;

    const newOrder = [...this.displayedColumns];
    newOrder.splice(currentIndex, 1);

    // Find the last frozen column position
    let insertPosition = 1; // After actions by default
    for (let i = newOrder.length - 1; i >= 0; i--) {
      const col = newOrder[i].toString();
      if (this.frozenColumns.includes(col) && col !== 'actions') {
        insertPosition = i + 1;
        break;
      }
    }

    newOrder.splice(insertPosition, 0, column as any);
    this.displayedColumns = newOrder;
    this.visibleColumns = newOrder;
    localStorage.setItem(`${this.tableName}_column_order`, JSON.stringify(newOrder));
  }

  /**
   * Repositions all columns according to their frozen state
   */
  private repositionAllColumns(): void {
    console.log('🔄 Repositioning all columns according to frozen state');

    // Start with a clean order: actions first, then all other columns
    const actionsColumn = this.displayedColumns.find(col => col === 'actions');
    const otherColumns = this.displayedColumns.filter(col => col !== 'actions');

    // Separate frozen and unfrozen columns
    const frozenCols = otherColumns.filter(col => this.frozenColumns.includes(col as string));
    const unfrozenCols = otherColumns.filter(col => !this.frozenColumns.includes(col as string));

    // Build new order: actions + frozen + unfrozen
    const newOrder = [
      ...(actionsColumn ? [actionsColumn] : []),
      ...frozenCols,
      ...unfrozenCols
    ] as (keyof T & string | 'actions')[];

    // Update the arrays
    this.displayedColumns = newOrder;
    this.visibleColumns = newOrder;

    // Save the new column order to localStorage
    localStorage.setItem(`${this.tableName}_column_order`, JSON.stringify(newOrder));

    console.log('🧊 New column order:', newOrder);
  }

  /** Called when user picks a different saved "view" in your <select> or something. */
  onViewSelectionChange(event: Event) {
    const selectedID = (event.target as HTMLSelectElement).value;
    const foundView = this.savedViews.find(v => v.settings_id === +selectedID);
    if (!foundView) return;

    localStorage.setItem(`${this.tableName}_current_settings_id`, selectedID);

    const tableSettingsToSend: TableSettings = {
      tableName: this.tableName,
      settings: foundView.settings ?? []
    };

    localStorage.setItem(`${this.tableName}_current_settings`, JSON.stringify(tableSettingsToSend));
    this.tableSettingsService.setServerSettings(tableSettingsToSend);
  }

  /** Called when user clicks on a column header to toggle sort. */
  toggleSortWrapper(column: keyof T | 'actions') {
    if (column === 'actions') return;
    this.dataSource.toggleSort(column as keyof T);
    localStorage.setItem(
      `${this.tableName}_current_sort_state`,
      JSON.stringify(this.dataSource._sortState)
    );
  }

  /** Return the table column setting for a given column. */
  getColumnSetting(column: string): TableColumnSetting | undefined {
    const s = this.tableSettingsService.tableSettingsSignal();
    return s?.settings.find(x => x.field_name === column);
  }

  /** If a column has a CSV list of valid <select> options, convert to array. */
  getSelectOptions(csv: string | undefined): string[] {
    if (!csv) return [];
    return csv.split(',').map(opt => opt.trim()).filter(opt => opt);
  }

  /** Style for a given column if e.g. you have backgroundColor set in field_calc_style. */
  getColumnStyle(column: string): { [key: string]: string } {
    const setting = this.tableSettingsService
                       .tableSettingsSignal()
                       ?.settings
                       .find(s => s.field_name === column);

    // start with an empty style object
    const style: { [key: string]: string } = {};

    /* background‑colour logic */
    if (setting?.field_calc_style) {
      style['backgroundColor'] = setting.field_calc_style;
    }
    
    if (setting?.field_min_width) {
      style['min-width'] = `${setting.field_min_width}px`;
    }

    /* max‑width logic */
    if (setting?.field_max_width) {
      const px = `${setting.field_max_width}px`;
      style['max-width']     = px;       // limit header & cell width
      style['width']         = px;       // makes the column a fixed width
      style['overflow']         = 'hidden';
      style['text-overflow'] = 'ellipsis';
      style['white-space']   = 'nowrap';
    }

    return style;
  }

  private isStatusCol = (c: string): boolean =>
  ['status', 'r_status', 'request_sh_status', 'request_pk_status'].includes(c.toLowerCase());

  /** return one or several CSS classes for this row */
  getRowClasses(row: any): { [cls: string]: boolean } {
    const status = this.statusFromRow(row);               // ex. 'Pending'
    /* map status → colour class */
    const className = `row-status-${status.replace(/\s+/g, '-').toLowerCase()}`;
    /* apply_style_to_row === 'yes' ? we add the class */
    const apply = this.getColumnSetting('r_status')?.apply_style_to_row === 'yes';
    return {
      [className]  : apply   // ex. 'row-status-pending': true/false
    };
  }

  /** return one or several CSS classes for this cell */
  getCellClasses(column: string | number | symbol, row?: any): { [cls: string]: boolean } {
    const key = String(column);            // avoid error TS (string | number | symbol)

    // Check if row has status color by using existing getRowStyle
    const hasStatusColor = row ? !!this.getRowStyle(row)['backgroundColor'] : false;

    return {
      'bg-white': this.isColumnFrozen(key) && !hasStatusColor,  /* bg-white only if frozen AND no status color */
      'py-2': key === 'references_data',    /* class for break line (col references_data) */
      //'whitespace-pre-line': key === 'references_data'    /* class for break line (col references_data) */
    };
  }



  getCellStyle(column: string, row: any): { [k:string]: string }  {
    const baseStyle = this.getColumnStyle(column);

    /* STATUS column – dynamic colour */
    if (this.isStatusCol(column)) {
      const bg = this.statusColours[this.statusFromRow(row)];
      return bg ? { ...baseStyle, backgroundColor: bg } : baseStyle;
    }

    /* FROZEN column with row status color */
    if (this.isColumnFrozen(column)) {
      const rowStyle = this.getRowStyle(row);
      if (rowStyle['backgroundColor']) {
        return { ...baseStyle, backgroundColor: rowStyle['backgroundColor'] };
      }
    }

     /* did the calc ask for an inline style? */
    const fn = this.compiledCalcs[column];
    if (fn) {
      const res = fn(row);
      if (res.style) { return { ...baseStyle, ...res.style }; }
    }

    /* fallback to the previous logic… */
    return baseStyle;
  }



  getRowStyle(row: any): { [k:string]: string } {
    const settings = this.getColumnSetting('status')   
                  ?? this.getColumnSetting('request_pk_status')
                  ?? this.getColumnSetting('request_sh_status')
                  ?? this.getColumnSetting('r_status');
    if (settings?.apply_style_to_row === 'yes') {
      const bg = this.statusColours[this.statusFromRow(row)];
      return bg ? { backgroundColor: bg } : {};
    }
    return {};
  }

  get groupByIsDate(): boolean {
    // nothing grouped → definitely not a date
    if (!this.dataSource?.currentGroupBy) { return false; }

    // `getColumnSetting` needs a string, so cast is fine
    const setting = this.getColumnSetting(
      this.dataSource.currentGroupBy as unknown as string
    );

    return setting?.field_is_date === 'yes';
  }

  openTableSettings(
    viewID?: string| null | undefined,
    settings?: TableColumnSetting
    ): void {
    const storedId       = viewID    ?? this.selectedViewId;
    const storedSettings = settings  ?? this.tableSettingsService.tableSettingsSignal();

    const tSettings = this.dialog.open(TableSettingsAdminFormComponent, {
        width: '2000px',
        maxHeight: '70vh',
        autoFocus: false,
        disableClose: true,
        data: {
          actionType: 'create',
          storedId: storedId,
          currentSettings: storedSettings,
          existingViews: this.savedViews
        }
      });
      tSettings.closed.subscribe(result => {
        console.log('The dialog was closed ', result );
        this.reloadAfterDialog();
      });
    }


    openReqComments(ref_id: string, req_type: string): void {
      console.log(ref_id)
      console.log('Emitting signal with:', ref_id, req_type);
      this.messageService.emitMessage(ref_id, req_type); // Optionally emit data via a service

      // If overlayRef does not exist yet, create it
      if (!this.overlayRef) {
        const positionStrategy = this.overlay.position().global().right('0').bottom('0');
        this.overlayRef = this.overlay.create({
          positionStrategy,
          hasBackdrop: false,
          backdropClass: 'backdrop'
        });
      }

      // Attach the component or update its content if already attached
      this.attachOrUpdateContent(ref_id, req_type);
    }

    //Get type and id according to dept
    getReqInfo(row: any): { id: any, type: string } | null {
      switch(this.requestType) {
        case 'MFG':
          return { id: row.requestID, type: 'mfg' };
        case 'Packout':
          return { id: row.request_pk_id, type: 'pk' };
        case 'Tag':
          return { id: row.tag_id, type: 'tag' };
        case 'Shipping':
          return { id: row.request_sh_id, type: 'ship' };
        case 'Project':
          return { id: row.projectmgntrecid, type: 'prj' };
        default:
          return { id: row.id, type: this.requestType };
      }
    }

    /** Called when user clicks on the "INFO" icon. */
    openInfoWindow(url: any){
      console.log(  'openInfoWindow() called with URL:', url);
      window.open(url, '_blank', 'width=1700,height=1000');
    }

    /** Called when user clicks on the "PRINT" icon. */
    printRequest(row: any){
      let url = 'https://reportal.intranet.lan/showReportFromTrustedIP.aspx?id=157&P1=' + row;
      window.open(url, '_blank', 'noopener,noreferrer,width=1200,height=1000');
    }

    //Get type and id according to dept
    getReqType(row: any):string | null {
      if (row.projectmgntrecid) return 'PM';
      if (row.requestID) return 'MFG';
      if (row.request_pk_id) 'PK';
      if (row.request_sh_id) return 'SH';
      return null; // No ID
    }

    /**
     * Attaches the SelectedMessageComponent to the same overlayRef
     * or updates its content if necessary.
     */
    private attachOrUpdateContent(ref_id: string, req_type: string): void {
      if (!this.overlayRef) return;

      // If a component is already attached, detach it to cleanly reload a new portal
      if (this.overlayRef.hasAttached()) {
        this.overlayRef.detach();
      }

      // Create a ComponentPortal for SelectedMessageComponent
      const portal = new ComponentPortal(SelectedMessageComponent);
      const componentRef = this.overlayRef.attach(portal);

      // Pass data to the child component
      componentRef.instance.reference_id = ref_id;
      componentRef.instance.requestType = req_type;
      componentRef.instance.isOverlay = true;

      // Subscribe to the "close" event emitted by the child component
      componentRef.instance.close.subscribe(() => {
        // Close the overlay from the parent side
        this.overlayRef?.dispose();
        this.overlayRef = null;
        console.log('Side panel closed (via button)');
      });
    }


    /** Called when user clicks on the "Edit" icon. */
    openEditForm(row: any){
      console.log('this is row: ', row)
        let editForm = this.dialog.open(this.editComponent, {
          width: '900px',
          maxHeight: '70vh',
          autoFocus: false,
          disableClose: true,
          panelClass: 'overflow-auto',
          data: {
            actionType: 'edit',
            curData: row
          }
        });
        editForm.closed.subscribe(result => {
          console.log('The dialog was closed');
          this.reloadAfterDialog();
        });
    }




  

    
}