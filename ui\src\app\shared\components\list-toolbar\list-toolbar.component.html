<!-- left section -->
<div class="top-bar-btns mt-16 px-4 flex items-center">
    <div class="flex gap-4 justify-start p-4 text-start min-w-1/3 z-10">

      <!-- “New …” button -->
      <button *ngIf="isRequestTable"
        type="button"
        (click)="create.emit()"
        class="text-gray-900 group-hover:text-white bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none 
            focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center cursor-pointer
        dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 "><span class="pl-1">New {{ requestType }} Request</span>
      </button>

      <!-- “Table Settings” button -->
      <button type="button"
        (click)="openSettings.emit()"
        class="text-gray-900 group-hover:text-white bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none 
            focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center cursor-pointer
            dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 ">
        <span class="pl-1">Table Settings</span>
      </button>

      <!-- Views Admin Settings dropdown -->
      <div class="relative w-[300px]">
        <!-- Dropdown button -->
        <button
          (click)="toggleTableSettingsDropdown()"
          class="w-full text-gray-900 bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center justify-between dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 cursor-pointer">
          <span>{{ getSelectedViewName() || 'Select a view' }}</span>
          <!-- Dropdown arrow -->
          <svg class="w-4 h-4 ml-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="m1 1 4 4 4-4"/>
          </svg>
        </button>

        <!-- Dropdown menu -->
        <div *ngIf="isAdminViewsOpen"
             class="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700">
          <!-- Default option -->
          <div
            (click)="onTableSettingSelect('')"
            class="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600">
            Select a view
          </div>

          <!-- Table settings views -->
          <div *ngFor="let view of savedViews"
               (click)="onTableSettingSelect(view.settings_id?.toString() || '')"
               class="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 cursor-pointer flex items-center">
            <!-- Selected indicator -->
            <span class="mr-2 text-green-600 font-bold">
              {{ (view.settings_id === selectedViewId || (view.settings_name === 'Default' && !selectedViewId)) ? '✓' : '○' }}
            </span>
            {{ view.settings_name }}
          </div>
        </div>
      </div>
      <!-- "Save as new view" button (optionnel) -->
      <button *ngIf="showSaveAsViewButton" type="button"
        (click)="onSaveAsViewClick()"
        class="text-gray-900 group-hover:text-white bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none
            focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center cursor-pointer
            dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 ">
        <span class="pl-1">Save as new view</span>
      </button>

      <!-- Saved Views dropdown (optionnel) -->
      <div *ngIf="showSaveAsViewButton" class="relative w-[250px]">
        <!-- Dropdown button -->
        <button
          (click)="toggleSavedViewsDropdown()"
          class="w-full text-gray-900 bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center justify-between dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 cursor-pointer">
          <span>{{ currentAppliedView ? 'Current View: ' + currentAppliedView : 'Select a saved view' }}</span>
          <!-- Dropdown arrow -->
          <svg class="w-3 h-3 ml-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="m1 1 4 4 4-4"/>
          </svg>
        </button>

        <!-- Dropdown menu -->
        <div *ngIf="isSavedViewsOpen"
             class="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700">
          <!-- Default option -->
          <div
            (click)="onSavedViewSelect('')"
            class="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600">
            Select a saved view
          </div>

          <!-- Saved views with delete icons -->
          <div *ngFor="let view of savedCustomViews"
               class="flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 group">
            <!-- View name (clickable) -->
            <span
              (click)="onSavedViewSelect(view.name)"
              class="flex-1 cursor-pointer flex items-center">
              <span class="mr-2 text-green-600 font-bold">{{ view.name === currentAppliedView ? '✓' : '○' }}</span>
              {{ view.name }}
            </span>

            <!-- Delete icon -->
            <button
              (click)="onDeleteView(view, $event)"
              class="ml-2 p-1 text-gray-400 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              title="Delete view">
              <!-- Trash icon -->
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- middle section -->
    <div class="place-self-center text-center flex-1">
      <span class="isolate inline-flex">
        {{ tempArrayLength }} {{ requestType }} Requests
      </span>
    </div>

    <!-- right section -->
    <div class="place-content-end text-end p-1 flex-1">
      <span class="text-sm text-gray-500">
        Last updated at {{ lastUpdatedTime }}
      </span>
    </div>
  </div>