<!-- left section -->
<div class="top-bar-btns mt-16 px-4 flex items-center">
    <div class="flex gap-4 justify-start p-4 text-start min-w-1/3 z-10">

      <!-- “New …” button -->
      <button *ngIf="isRequestTable"
        type="button"
        (click)="create.emit()"
        class="text-gray-900 group-hover:text-white bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none 
            focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center cursor-pointer
        dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 "><span class="pl-1">New {{ requestType }} Request</span>
      </button>

      <!-- “Table Settings” button -->
      <button type="button"
        (click)="openSettings.emit()"
        class="text-gray-900 group-hover:text-white bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none 
            focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center cursor-pointer
            dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 ">
        <span class="pl-1">Table Settings</span>
      </button>

      <!-- views dropdown -->
      <select
        (change)="onSelect($event)"
        class="w-[300px] text-gray-900 bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 cursor-pointer appearance-none
        bg-no-repeat 
        bg-[length:.75rem]
        bg-[url('data:image/svg+xml,%3Csvg%20aria-hidden%3D%27true%27%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20fill%3D%27none%27%20viewBox%3D%270%200%2010%206%27%3E%3Cpath%20stroke%3D%27%236B7280%27%20stroke-linecap%3D%27round%27%20stroke-linejoin%3D%27round%27%20stroke-width%3D%271%27%20d%3D%27m1%201%204%204%204-4%27%2F%3E%3C%2Fsvg%3E')]
        bg-[position:right_15px_top_12px]">
        <option>Select a view</option>
        <option *ngFor="let view of savedViews"
        [value]="view.settings_id"
        [selected]="view.settings_id === selectedViewId || view.settings_name == 'Default'">
        {{ view.settings_name }}
      </option>
    </select>
      <!-- "Save as view" button (optionnel) -->
      <button *ngIf="showSaveAsViewButton" type="button"
        (click)="onSaveAsViewClick()"
        class="text-gray-900 group-hover:text-white bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none
            focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center cursor-pointer
            dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 ">
        <span class="pl-1">Save as view</span>
      </button>

      <!-- Saved Views dropdown (optionnel) -->
      <select *ngIf="showSaveAsViewButton"
        (change)="onSavedViewSelect($event)"
        #savedViewSelect
        class="w-[250px] text-gray-900 bg-white hover:border-gray-400 border border-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-1.5 inline-flex items-center dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 cursor-pointer appearance-none
            bg-no-repeat
            bg-[length:.75rem]
            bg-[url('data:image/svg+xml,%3Csvg%20aria-hidden%3D%27true%27%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20fill%3D%27none%27%20viewBox%3D%270%200%2010%206%27%3E%3Cpath%20stroke%3D%27%236B7280%27%20stroke-linecap%3D%27round%27%20stroke-linejoin%3D%27round%27%20stroke-width%3D%271%27%20d%3D%27m1%201%204%204%204-4%27%2F%3E%3C%2Fsvg%3E')]
            bg-[position:right_15px_top_12px]">
        <option value="">Select a saved view</option>
        <option *ngFor="let view of savedCustomViews" [value]="view.name">
          📋 {{ view.name }}
        </option>
      </select>
    </div>

    <!-- middle section -->
    <div class="place-self-center text-center flex-1">
      <span class="isolate inline-flex">
        {{ tempArrayLength }} {{ requestType }} Requests
      </span>
    </div>

    <!-- right section -->
    <div class="place-content-end text-end p-1 flex-1">
      <span class="text-sm text-gray-500">
        Last updated at {{ lastUpdatedTime }}
      </span>
    </div>
  </div>