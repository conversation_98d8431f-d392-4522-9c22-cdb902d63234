// src/app/shared/components/list-toolbar/list-toolbar.component.ts
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableSettingsFromDB } from '../../../core/models/table-settings';

@Component({
  selector: 'app-list-toolbar',
  imports: [CommonModule],
  templateUrl: './list-toolbar.component.html',
})
export class ListToolbarComponent {

  /* ---------- Inputs ---------- */
  @Input() newLabel          = 'New Item';
  @Input() savedViews: TableSettingsFromDB[] = [];
  @Input() selectedViewId: number | null = null;
  @Input() tempArrayLength   = 0;
  @Input() requestType       = '';
  @Input() lastUpdatedTime   = '';
  /* Optional - to hide btn 'new sync' when is false*/
  @Input() isRequestTable: boolean = true;
  @Input() savedCustomViews: any[] = [];
  @Input() showSaveAsViewButton: boolean = false;
  @Input() currentAppliedView: string = '';

  /* ---------- Outputs ---------- */
  @Output() create       = new EventEmitter<void>();
  @Output() openSettings = new EventEmitter<void>();
  @Output() saveAsView   = new EventEmitter<void>();
  @Output() viewSelected = new EventEmitter<Event>();
  @Output() customViewSelected = new EventEmitter<string>();

  /* ---------- internal helper ---------- */
  onSelect(event: Event): void {
    this.viewSelected.emit(event);   // emit the raw DOM event
  }

  onSaveAsViewClick(): void {
    console.log('Save as view button clicked in list-toolbar');
    this.saveAsView.emit();
  }

  onSavedViewSelect(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const viewName = target.value;
    if (viewName) {
      this.customViewSelected.emit(viewName);
      // Reset dropdown to default after selection
      setTimeout(() => {
        target.value = '';
      }, 100);
    }
  }
}
