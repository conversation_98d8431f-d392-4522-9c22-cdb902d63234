import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DialogRef, DIALOG_DATA } from '@angular/cdk/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

export interface SaveViewDialogData {
  tableName: string;
  currentViewName?: string;  // Current view name if user has one
  hasExistingViews: boolean; // Whether user has any saved views
}

export interface SaveViewDialogResult {
  viewName: string;
  isEdit: boolean;  // true = edit current, false = save as new
}

@Component({
  selector: 'app-save-view-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule
  ],
  template: `
    <div class="p-6 bg-white rounded-lg shadow-lg">
      <h2 class="text-xl font-semibold mb-4">Save View</h2>

      <p class="text-gray-600 mb-4">
        Save the current filters, sorting, and grouping for "{{ data.tableName }}".
      </p>

      <!-- Options radio buttons -->
      <div class="mb-4" *ngIf="data.hasExistingViews && data.currentViewName">
        <div class="space-y-2">
          <label class="flex items-center">
            <input
              type="radio"
              [(ngModel)]="selectedOption"
              [ngModelOptions]="{standalone: true}"
              value="edit"
              class="mr-2">
            <span>Edit "{{ data.currentViewName }}"</span>
          </label>
          <label class="flex items-center">
            <input
              type="radio"
              [(ngModel)]="selectedOption"
              [ngModelOptions]="{standalone: true}"
              value="new"
              class="mr-2">
            <span>Save as a new view</span>
          </label>
        </div>
      </div>

      <!-- View name input (only show for new views) -->
      <div class="w-full mb-4" *ngIf="selectedOption === 'new' || !data.hasExistingViews">
        <label for="viewName" class="block text-sm font-medium text-gray-700 mb-2">View Name</label>
        <input
          class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2 placeholder-gray-500"
          type="text"
          [(ngModel)]="viewName"
          [ngModelOptions]="{standalone: true}"
          (input)="onInputChange($event)"
          placeholder="Enter a name for this view"
          (keyup.enter)="save()"
          #nameInput>
      </div>

      <div class="flex justify-end gap-3">
        <button
          mat-button
          (click)="cancel()"
          class="px-4 py-2">
          Cancel
        </button>
        <button
          mat-raised-button
          color="primary"
          (click)="save()"
          [disabled]="!canSave()"
          class="px-4 py-2">
          {{ getSaveButtonText() }}
        </button>
      </div>
    </div>
  `
})
export class SaveViewDialogComponent {
  viewName = '';
  selectedOption: 'edit' | 'new' = 'edit';

  constructor(
    public dialogRef: DialogRef<SaveViewDialogResult>,
    @Inject(DIALOG_DATA) public data: SaveViewDialogData
  ) {
    // Set default option based on whether user has existing views
    if (!this.data.hasExistingViews || !this.data.currentViewName) {
      this.selectedOption = 'new';
    }
  }

  onInputChange(event: any): void {
    this.viewName = event.target.value;
  }

  canSave(): boolean {
    if (this.selectedOption === 'edit') {
      return true; // Can always edit existing view
    }
    return this.viewName?.trim() !== ''; // Need name for new view
  }

  getSaveButtonText(): string {
    if (this.selectedOption === 'edit') {
      return 'Update View';
    }
    return 'Save New View';
  }

  save(): void {
    if (this.canSave()) {
      const result: SaveViewDialogResult = {
        viewName: this.selectedOption === 'edit' ? this.data.currentViewName! : this.viewName.trim(),
        isEdit: this.selectedOption === 'edit'
      };
      this.dialogRef.close(result);
    }
  }

  cancel(): void {
    this.dialogRef.close();
  }
}
