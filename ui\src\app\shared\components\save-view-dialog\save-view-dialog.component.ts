import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DialogRef, DIALOG_DATA } from '@angular/cdk/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

export interface SaveViewDialogData {
  tableName: string;
}

export interface SaveViewDialogResult {
  viewName: string;
}

@Component({
  selector: 'app-save-view-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule
  ],
  template: `
    <div class="p-6 bg-white rounded-lg shadow-lg">
      <h2 class="text-xl font-semibold mb-4">Save Current View</h2>
      
      <p class="text-gray-600 mb-4">
        Save the current filters, sorting, and grouping for "{{ data.tableName }}" as a new view.
      </p>
      
      <form class="w-full mb-4">
        <label for="viewName">View Name</label>
        <input 
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2 ml-3 placeholder-gray-500"
          matInput 
          [(ngModel)]="viewName" 
          placeholder="Enter a name for this view"
          (keyup.enter)="save()"
          #nameInput>
      </form>
      
      <div class="flex justify-end gap-3">
        <button 
          mat-button 
          (click)="cancel()"
          class="px-4 py-2">
          Cancel
        </button>
        <button 
          mat-raised-button 
          color="primary"
          (click)="save()"
          [disabled]="!viewName?.trim()"
          class="px-4 py-2">
          Save View
        </button>
      </div>
    </div>
  `
})
export class SaveViewDialogComponent {
  viewName = '';

  constructor(
    public dialogRef: DialogRef<SaveViewDialogResult>,
    @Inject(DIALOG_DATA) public data: SaveViewDialogData
  ) {}

  save(): void {
    if (this.viewName?.trim()) {
      this.dialogRef.close({ viewName: this.viewName.trim() });
    }
  }

  cancel(): void {
    this.dialogRef.close();
  }
}
