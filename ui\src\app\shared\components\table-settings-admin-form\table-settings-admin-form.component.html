<div class="relative">
  <!-- Close Icon at Top Right -->
  <button
    type="button"
    (click)="closeDialog()"
    class="absolute top-0 right-0 m-2 text-gray-600 hover:text-gray-900 focus:outline-none cursor-pointer"
    aria-label="Close dialog"
  >
    <!-- Inline SVG for the X icon -->
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
         viewBox="0 0 24 24" stroke="currentColor">
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M6 18L18 6M6 6l12 12"
      />
    </svg>
  </button>

  <a href="https://example.com"
   target="_blank"
   rel="noopener noreferrer"
   onclick="window.open('/info_listofpipes',
                        'popupWindow',
                        'width=1200,height=800,scrollbars=yes'); return false;"> ? </a>

  <div class="h-[80vh] overflow-y-auto">
    <div class="max-w-full mx-auto p-4">
      <form [formGroup]="settingsForm" (ngSubmit)="onSubmit()">
        <!-- Table Name Select -->
        <div class="mb-6">
          <label for="tableName" class="block mb-2 text-sm font-medium text-gray-900">
            Select Table
          </label>
          <select
            id="tableName"
            formControlName="tableName"
            class="max-w-lg bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                   rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-2.5
                   appearance-none bg-no-repeat bg-[length:.75rem] bg-[position:right_15px_top_15px]
                   bg-[url('data:image/svg+xml,%3csvg%20aria-hidden=%27true%27%20xmlns=%27http://www.w3.org/2000/svg%27%20fill=%27none%27%20viewBox=%270%200%2010%206%27%3e%3cpath%20stroke=%27%236B7280%27%20stroke-linecap=%27round%27%20stroke-linejoin=%27round%27%20stroke-width=%272%27%20d=%27m1%201%204%204%204-4%27/%3e%3c/svg%3e')]"
          >
            <option value="">Choose a table</option>
            <option *ngFor="let table of tableNames" 
              [value]="table.table_name" 
              [selected]="this.data.currentSettings?.tableName">{{ table.table_name }}</option>
            <!-- <option value="MFG Request Table">MFG Request Table</option>
            <option value="Packout Request Table">Packout Request Table</option>
            <option value="PM Request Table">PM Request Table</option>
            <option value="Sephora Import Table">Sephora Import Table</option>
            <option value="Sephora Master Status Tracker Table">Sephora Master Status Tracker Table</option>
            <option value="Shipping Request Table">Shipping Request Table</option>
            <option value="Sync Status Table">Sync Status Table</option>
            <option value="Tag Request Table">Tag Request Table</option> -->
            
            <!-- additional table options -->
          </select>
        </div>

        
        <div *ngIf="settingsForm.get('tableName')?.value" class="mb-6 flex">
          <div class="flex flex-col min-w-[300px] p-2">
            <label for="tableName" class="block mb-2 text-sm font-medium text-gray-900">
              Select a View
            </label>
            <select
              id="settingsID"
              formControlName="settingsID"
              (change)="onViewSelectionChange($event)"
              class="max-w-lg bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                    rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-2.5"
            >
              <option value="none">Select a view</option>
              <option *ngFor="let view of savedViews" [value]="view.settings_id">
                {{ view.settings_name }}
              </option>
            </select>
          </div>

          <div class="flex flex-col min-w-[300px] p-2">  
            <label for="settingsNameTop" class="block mb-2 text-sm font-medium text-gray-900">
              Settings Name
            </label>
            <input
              id="settingsName"
              type="text"
              formControlName="settingsName"
              class="max-w-lg bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                    rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-2.5"
              placeholder="Enter a name for these settings..."
            />
          </div>

          <div class="flex flex-col p-2 self-end"> 
            <button
              type="submit"
              [disabled]="!settingsForm.valid"
              class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 border border-blue-300
                    focus:outline-none focus:ring-blue-300 font-medium 
                    rounded-lg text-sm p-2.5"
            >
              Save Settings
            </button>
          </div>  
        </div>

        <!-- Column Settings -->
        <div
          formArrayName="settings"
          cdkDropList
          (cdkDropListDropped)="drop($event)"
          class="space-y-4"
        >
          <div
            *ngFor="let setting of settingsControls; let i = index"
            [formGroupName]="i"
            cdkDrag
            class="flex flex-row flex-wrap items-start gap-4 mb-4 p-4 border rounded bg-white hover:shadow-md transition-shadow duration-200 relative"
          >
            <!-- Drag Handle -->
            <div class="absolute top-2 right-2 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100" cdkDragHandle title="Drag to reorder">
              <svg class="w-5 h-5 text-gray-400 hover:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M7 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"/>
              </svg>
            </div>

            <!-- Field Order Indicator -->
            <div class="absolute top-2 left-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
              {{ i + 1 }}
            </div>
            <!-- Field Name (read-only) -->
            <div class="flex flex-col min-w-[300px]">
              <label for="field_name-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Field Name
              </label>
              <input
                id="field_name-{{ i }}"
                type="text"
                formControlName="field_name"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
                readonly
              />
            </div>

            <!-- Display Name -->
            <div class="flex flex-col min-w-[300px]">
              <label for="field_name_display-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Display Name
              </label>
              <input
                id="field_name_display-{{ i }}"
                type="text"
                formControlName="field_name_display"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
              />
            </div>

            <!-- Hidden -->
            <div class="flex flex-col min-w-[100px]">
              <label for="field_hidden-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Hidden
              </label>
              <select
                id="field_hidden-{{ i }}"
                formControlName="field_hidden"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
              >
                <option *ngFor="let option of yesNoOptions" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <!-- Is Date -->
            <div class="flex flex-col min-w-[100px]">
              <label for="field_is_date-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Is&nbsp;Date
              </label>
              <select
                id="field_is_date-{{ i }}"
                formControlName="field_is_date"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                      rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2">
                <option *ngFor="let option of yesNoOptions" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>


            <!-- Is Date-Time -->
            <div class="flex flex-col min-w-[120px]">
              <label for="field_is_date_time-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Is Date-Time
              </label>
              <select
                id="field_is_date_time-{{ i }}"
                formControlName="field_is_date_time"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                      rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2">
                <option *ngFor="let option of yesNoOptions" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <!-- Has Filter -->
            <div class="flex flex-col min-w-[100px]">
              <label for="field_has_filter-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Filter Available
              </label>
              <select
                id="field_has_filter-{{ i }}"
                formControlName="field_has_filter"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
              >
                <option *ngFor="let option of yesNoOptions" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <!-- Is Groupable -->
            <div class="flex flex-col min-w-[100px]">
              <label for="field_is_groupable-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Groupable
              </label>
              <select
                id="field_is_groupable-{{ i }}"
                formControlName="field_is_groupable"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
              >
                <option *ngFor="let option of yesNoOptions" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <!-- Editable -->
            <div class="flex flex-col min-w-[100px]">
              <label for="field_editable-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Editable
              </label>
              <select
                id="field_editable-{{ i }}"
                formControlName="field_editable"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
              >
                <option *ngFor="let option of yesNoOptions" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <!-- Show Editable Type and Editable Select Values only if Editable is "yes" -->
            <div *ngIf="setting.get('field_editable')?.value === 'yes'">
              <!-- Editable Type -->
              <div class="flex flex-col min-w-[120px]">
                <label for="field_editable_type-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                  Editable Type
                </label>
                <select
                  id="field_editable_type-{{ i }}"
                  formControlName="field_editable_type"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                         rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
                >
                  <option *ngFor="let option of editableTypeOptions" [value]="option">
                    {{ option }}
                  </option>
                </select>
              </div>
              
              <!-- CSV Values if "select" -->
              <div *ngIf="setting.get('field_editable_type')?.value === 'select'" class="flex flex-col min-w-[150px]">
                <label for="field_editable_select_values-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                  Editable Select Values (CSV)
                </label>
                <input
                  id="field_editable_select_values-{{ i }}"
                  type="text"
                  formControlName="field_editable_select_values"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                         rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
                />
              </div>
            </div>

              <!-- *** ROW BREAK *** -->
            <div class="basis-full h-0"></div>

            <!-- Min Width -->
          <div class="flex flex-col min-w-[120px] max-w-[120px]">
            <label for="field_min_width-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
              Min&nbsp;Width&nbsp;(px) 
            </label>
            <input id="field_min_width-{{ i }}"
                  type="number" min="1"
                  formControlName="field_min_width"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm
                          rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
                  placeholder="none" />
          </div>

            <!-- Max Width -->
            <div class="flex flex-col min-w-[120px] max-w-[120px]">
              <label for="field_max_width-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Max&nbsp;Width&nbsp;(px) 
              </label>
              <input id="field_max_width-{{ i }}"
                     type="number"
                     min="1"
                     formControlName="field_max_width"
                     class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                            rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
                     placeholder="none" />
            </div>


            <!-- Has Tooltip -->
            <div class="flex flex-col min-w-[120px]">
              <label for="field_has_tooltip-{{ i }}"
                    class="mb-1 text-xs font-medium text-gray-700">
                Has&nbsp;Tooltip
              </label>
              <select
                id="field_has_tooltip-{{ i }}"
                formControlName="field_has_tooltip"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm
                      rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2">
                <option *ngFor="let option of yesNoOptions" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>

          

            <!-- Trigger Action -->
            <div class="flex flex-col min-w-[120px]">
              <label for="field_trigger_action-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Trigger Action
              </label>
              <select
                id="field_trigger_action-{{ i }}"
                formControlName="field_trigger_action"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
              >
                <option value="">None</option>
                <option *ngFor="let option of triggerActionOptions" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <!-- Trigger Action Ref Field -->
            <div class="flex flex-col min-w-[150px]">
              <label for="field_trigger_action_ref_field-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Trigger Action Ref Field
              </label>
              <select
                id="field_trigger_action_ref_field-{{ i }}"
                formControlName="field_trigger_action_ref_field"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2 max-w-[300px]"
              >
                <option value="">Select Field</option>
                <option *ngFor="let option of availableFields" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <!-- Calculation -->
            <div class="flex flex-col min-w-[300px]">
              <label for="field_calc-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Calculation
              </label>
              <input
                id="field_calc-{{ i }}"
                type="text"
                formControlName="field_calc"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
              />
            </div>

            <!-- Calculation Style (Color Picker) -->
            <div class="flex flex-col min-w-[120px]">
              <label for="field_calc_style-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Calculation Style (Choose Color)
              </label>
              <div class="flex items-center space-x-2">
                <input
                  id="field_calc_style-{{ i }}"
                  type="color"
                  formControlName="field_calc_style"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                         rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
                />
                <!-- Display the chosen color block -->
                <div
                  class="w-8 h-8 border border-gray-300 rounded"
                  [style.backgroundColor]="setting.get('field_calc_style')?.value || '#ffffff'"
                >
                </div>
                <!-- Optionally, display the color value as text -->
                <span class="text-sm">
                  {{ setting.get('field_calc_style')?.value || '#ffffff' }}
                </span>
              </div>
            </div>


            <!-- Apply style to row? -->
            <div class="flex flex-col min-w-[120px]">
              <label class="mb-1 text-xs font-medium text-gray-700">
                Apply Color to Row
              </label>
              <select formControlName="apply_style_to_row"
                      class="bg-gray-50 border border-gray-300 text-sm rounded-lg p-2">
                <option value="no">no</option>
                <option value="yes">yes</option>
              </select>
            </div>


            <!-- Calculation Ref Field -->
            <div class="flex flex-col min-w-[120px]  max-w-[300px]">
              <label for="field_calc_ref_field-{{ i }}" class="mb-1 text-xs font-medium text-gray-700">
                Calc Ref Field
              </label>
              <select
                id="field_calc_ref_field-{{ i }}"
                formControlName="field_calc_ref_field"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm 
                       rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
              >
                <option value="">Select Field</option>
                <option *ngFor="let option of availableFields" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>




            <!-- Remove Button -->
            <div class="flex flex-col self-end">
              <button
                type="button"
                (click)="removeSetting(i)"
                class="whitespace-nowrap text-gray-900 bg-white hover:bg-gray-100 
                       border border-gray-200 focus:ring-4 focus:outline-none 
                       focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 
                       text-center inline-flex items-center dark:focus:ring-gray-600 
                       dark:bg-gray-800 dark:border-gray-700 dark:text-white 
                       dark:hover:bg-gray-700 me-2 mb-2"
              >
                Reset Settings
              </button>
            </div>
          </div>
        </div>

        <!-- Add New Column Setting Button -->
        <div *ngIf="settingsForm.get('tableName')?.value" class="mb-6">
          <button
            type="button"
            (click)="addSetting()"
            class="text-white bg-gray-600 hover:bg-gray-700 focus:ring-4 
                   focus:outline-none focus:ring-gray-300 font-medium 
                   rounded-lg text-sm px-4 py-2"
          >
            Add Column Setting
          </button>
        </div>

        <!-- Submit Button -->
        <div>
          <button
            type="submit"
            [disabled]="!settingsForm.valid"
            class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 
                   focus:outline-none focus:ring-blue-300 font-medium 
                   rounded-lg text-sm px-4 py-2"
          >
            Save Settings
          </button>
        </div>
      </form>
    </div>
  </div>
</div>