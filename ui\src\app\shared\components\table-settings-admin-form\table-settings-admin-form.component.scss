:host {
    display: block;
    background: #fff;
    border-radius: 8px;
    padding: 8px 16px 16px;
    overflow-y: auto;
    overflow-x: hidden;
  }

// Drag & Drop Styles
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25),
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
  background: white;
  opacity: 0.95;
  transform: rotate(1deg) scale(1.02);
  border: 2px solid #3b82f6;

  // Hide the drag handle and order indicator in preview
  .absolute {
    display: none;
  }
}

.cdk-drag-placeholder {
  opacity: 0.4;
  border: 2px dashed #3b82f6;
  background: #eff6ff;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: "Drop here";
    color: #3b82f6;
    font-weight: 500;
    font-size: 14px;
  }
}

.cdk-drag-animating {
  transition: transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.cdk-drop-list-dragging .cdk-drag:not(.cdk-drag-placeholder) {
  transition: transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

// Enhanced drag handle styles
[cdkDragHandle] {
  transition: all 200ms ease;

  &:hover {
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1.1);
  }

  &:active {
    background-color: rgba(59, 130, 246, 0.2);
    transform: scale(0.95);
  }
}

// Draggable item styles
[cdkDrag] {
  transition: box-shadow 200ms ease, transform 200ms ease;

  &:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  &.cdk-drag-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}