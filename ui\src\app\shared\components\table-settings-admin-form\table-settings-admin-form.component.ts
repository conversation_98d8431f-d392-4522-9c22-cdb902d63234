import { Component, Inject, OnInit } from '@angular/core';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { CdkDragDrop, CdkDrag, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { TableSettings, TableColumnSetting, TableSettingsSearchObj, TableSettingsFromDB } from '../../../core/models/table-settings';
import { TableSettingsService } from '../../../core/services/table-settings.service';
import { SnackNotifService } from '../../../core/services/snack-notif.service';
import { distinctUntilChanged } from 'rxjs';

@Component({
  selector: 'app-table-settings-admin-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, CdkDrag, CdkDropList],
  templateUrl: './table-settings-admin-form.component.html',
  styleUrls: ['./table-settings-admin-form.component.scss']
})
export class TableSettingsAdminFormComponent implements OnInit {
  // Main form holding the table name and column settings array.
  settingsForm: FormGroup;

  // Options for yes/no fields.
  yesNoOptions = ['yes', 'no'];
  // Options for the type of editor.
  editableTypeOptions = ['text', 'select', 'date'];
  // Options for trigger actions.
  triggerActionOptions = ['email', 'send_comment'];
  // Options for tables select


  // A list of available fields (used in select dropdowns).
  availableFields: string[] = [];

  search_obj: TableSettingsSearchObj = {}
  tempArray: any
  tableNames: any
  savedViews: TableSettingsFromDB[] = [];
 
  constructor(
    private fb: FormBuilder,
    private tableSettingsService: TableSettingsService,
    private dialogRef: DialogRef<TableSettingsAdminFormComponent>,
    public snotif: SnackNotifService,
    @Inject(DIALOG_DATA) public data: {
      actionType: string;
      storedId?: number;
      currentSettings?: TableSettings;
      existingViews?: TableSettingsFromDB[];
    }
  ) {
    this.settingsForm = this.fb.group({
      tableName: ['', Validators.required],
      settingsID: [''], // set this when the form gets submitted. 
      settingsName: ['', Validators.required],
      currentUser: [localStorage.getItem('displayname')],
      settings: this.fb.array([]),
    });
  }
 
  ngOnInit(): void {
    // get all table names:
    this.tableSettingsService.getTableNames()
      .subscribe({
        next: (t_res: any)=>{
          console.log(t_res)
          this.tableNames = t_res
        }
      })

    // get settings name from DB using service
    console.log('Received data:', this.data);
    // If we have a currentSettings object, apply it
    if (this.data.currentSettings) {
      //Set the table name:
      const tableName = this.data.currentSettings.tableName ?? '';
      this.settingsForm.get('tableName')?.setValue(tableName, { emitEvent: false });
      // populate savedViews:
      if(this.data.existingViews){
        this.savedViews = this.data.existingViews;
      }
      
      // Force the standard columns for that table to load:
      //    (so your form array is primed with default fields)
      this.onTableNameChange(tableName, false);

      //  Now actually populate the form array with user settings
      this.populateForm(this.data.currentSettings);

      // If you store a “settingsName” in the DB or pass it from currentSettings, set it:
      // For example, if you store it at the root level or in the DB:
      // this.settingsForm.get('settingsName')?.setValue(this.data.currentSettings.settingsName || '');
       // If we got a stored ID, set it in the form
      if (this.data.storedId) {
        this.settingsForm.get('settingsID')?.setValue(Number(this.data.storedId));
      }
      if(this.data.existingViews){
        const findSettingsName = (id: Number) => {
          const match = this.savedViews.find(item => item.settings_id === id);
          return match ? match.settings_name : null;
        };
        this.settingsForm.get('settingsName')?.setValue(findSettingsName(Number(this.data.storedId)));
      }
    }
    // When the table name changes, reinitialize the form.
    this.settingsForm.get('tableName')?.valueChanges
      .pipe(distinctUntilChanged())
      .subscribe(value => {
        this.onTableNameChange(value);
        this.getSavedSettings(value);
    });

    // Subscribe to form value changes and update the signal only if the form is valid.
    this.settingsForm.valueChanges.subscribe(() => {
      const rawSettings: TableSettings = this.settingsForm.getRawValue();
      //console.log('This is rawSettings: ', rawSettings);
      this.tableSettingsService.tableSettingsSignal.set(rawSettings);
    });
  }

  //use the API to go get all the saved settings
  getSavedSettings(tableName: string){
    this.search_obj.table_name = tableName
    this.tableSettingsService.getServerSettings(this.search_obj).subscribe({
      next: (response) => {
        console.log(response.body)
        this.savedViews = response.body
      },
      error: (error) => console.error('Error getting table settings: ', error)
    })
  }

  //set the same inside settingsName
  onViewSelectionChange(event: Event): void {
    // Clear existing settings
    const settingsArray = this.settingsForm.get('settings') as FormArray;
    settingsArray.clear();

    const selectedID = (event.target as HTMLSelectElement).value;
    //if selectedID = 'none' reset settingsID 
    if(selectedID=='none'){
      this.settingsForm.get('settingsName')?.setValue('')
      this.onTableNameChange(this.settingsForm.get('tableName')?.value)
    } else {
      const selectedView = this.savedViews.find(
        (view: TableSettingsFromDB) => view.settings_id === Number(selectedID)
      );
      
  
      // If settings are stored as JSON, be sure to parse them:
      const parsedSettings = typeof selectedView?.settings === 'string'
        ? JSON.parse(selectedView.settings)
        : selectedView?.settings;
  
      // Push each setting from the selected view
      parsedSettings.forEach((setting: TableColumnSetting) => {
        // skip "actions" if desired
        if (setting.field_name === 'actions') return;
        settingsArray.push(this.createSettingGroup(setting));
      });
    
      if (selectedView) {
        this.settingsForm.get('settingsName')?.setValue(selectedView.settings_name);
      } 
    }
    
  }


  // this.tableSettingsService.upsertTableSettings(formValue).subscribe({
  //   next: (response) => {
  //     console.log('Settings saved successfully', response)
  //     this.snotif.notify('Table settings updated successfully.')
  //   },
  //   error: (error) => console.error('Error saving settings', error)
  // });



 
  /**
   * Populates the form with loaded settings.
   * Filters out any settings for the "actions" column.
   */
  populateForm(settings: TableSettings): void {
    this.settingsForm.patchValue(
      {tableName: settings.tableName},
      { emitEvent: false } 
    );
    const settingsArray = this.settingsForm.get('settings') as FormArray;
    settingsArray.clear();

   
    // Only add settings if the field is not "actions".
    settings.settings.forEach((setting: TableColumnSetting) => {
      if (setting.field_name !== 'actions') {
        settingsArray.push(this.createSettingGroup(setting));
      }
    });
  }
 
  /**
   * Returns a FormGroup for a single column setting.
   * – The **field_name** control is disabled to prevent editing.
   */
  createSettingGroup(setting?: TableColumnSetting): FormGroup {
    return this.fb.group({
      // Disable field_name so its value remains unchanged.
      field_name: [{ value: setting ? setting.field_name : '', disabled: true }, Validators.required],
      // field_name_display is editable and must not be empty.
      field_name_display: [setting ? setting.field_name_display : '', [Validators.required, Validators.minLength(1)]],
      field_hidden: [setting ? setting.field_hidden : 'no', Validators.required],
      field_editable: [setting ? setting.field_editable : 'no', Validators.required],
      field_editable_type: [setting ? setting.field_editable_type : 'text', Validators.required],
      field_editable_select_values: [setting ? setting.field_editable_select_values : ''], 
      field_trigger_action: [setting ? setting.field_trigger_action : ''],
      field_trigger_action_ref_field: [setting ? setting.field_trigger_action_ref_field : ''],
      field_calc: [setting ? setting.field_calc : ''],
      field_calc_style: [setting ? setting.field_calc_style : ''],
      apply_style_to_row: [setting?.apply_style_to_row ?? 'no'],
      field_calc_ref_field: [setting ? setting.field_calc_ref_field : ''],
      field_has_filter: [setting ? setting.field_has_filter : 'no'],
      field_is_groupable: [setting ? setting.field_is_groupable : 'no'],
      field_is_date:      [setting ? setting.field_is_date      : 'no'],
      field_is_date_time: [setting ? setting.field_is_date_time : 'no'],
      field_min_width: [setting?.field_min_width ?? null],
      field_max_width: [setting?.field_max_width ?? null],  
      
      field_has_tooltip: [setting? setting.field_has_tooltip:  'no', ]
    });
  }
 
  // Convenience getter for the settings FormArray controls.
  get settingsControls() {
    return (this.settingsForm.get('settings') as FormArray).controls;
  }

  
  
  /**
   * Adds a new (empty) column setting.
   */
  addSetting(): void {
    const settingsArray = this.settingsForm.get('settings') as FormArray;
    settingsArray.push(this.createSettingGroup());
  }
 
  /**
   * Removes the column setting at the given index.
   */
  removeSetting(index: number): void {
    const settingsArray = this.settingsForm.get('settings') as FormArray;
    settingsArray.removeAt(index);
  }

  /**
   * Handles drag & drop reordering of column settings.
   */
  drop(event: CdkDragDrop<any[]>): void {
    // Only proceed if the item was actually moved
    if (event.previousIndex === event.currentIndex) {
      return;
    }

    const settingsArray = this.settingsForm.get('settings') as FormArray;

    // Get the current controls array
    const controls = settingsArray.controls;

    // Get the field name for user feedback
    const movedFieldName = controls[event.previousIndex].get('field_name')?.value;

    // Move the item in the array
    moveItemInArray(controls, event.previousIndex, event.currentIndex);

    // Clear the FormArray and re-add controls in new order
    settingsArray.clear();
    controls.forEach(control => settingsArray.push(control));

    // Update the form to trigger change detection
    this.settingsForm.updateValueAndValidity();

    // Provide user feedback
    const direction = event.currentIndex < event.previousIndex ? 'up' : 'down';
    console.log(`Column "${movedFieldName}" moved ${direction} from position ${event.previousIndex + 1} to ${event.currentIndex + 1}`);

    // Optional: Show a brief notification
    this.snotif.notify(`Column "${movedFieldName}" moved to position ${event.currentIndex + 1}`);
  }
 
  closeDialog(): void {
    this.dialogRef.close();
  }
 
  /**
   * Called when the form is submitted.
   * Uses getRawValue() to include disabled controls (i.e. field_name) in the output.
   */
  onSubmit(): void {
    if (this.settingsForm.valid) {
      const formValue: TableSettings = this.settingsForm.getRawValue();

      const cleanedSettings = formValue.settings.map((setting: TableColumnSetting) => {
        const cleanedSetting: Partial<TableColumnSetting> = {};
  
        (Object.keys(setting) as (keyof TableColumnSetting)[]).forEach(key => {
          const value = setting[key];
          if (value === null || value === undefined) {
            // Type-safe handling based on original type
            console.log(cleanedSetting[key])
            cleanedSetting[key] = '' as any;  // safely using 'any' here
            console.log(cleanedSetting[key])
          } else {
            cleanedSetting[key] = value as any;
          }
        });
  
        return cleanedSetting;
      });
      const preparedFormValue = {
        ...formValue,
        settings: JSON.stringify(formValue.settings),
      };
      
      console.log('############### form value ####################')
      console.log(preparedFormValue)
      console.log('############### end form value ####################')
      //this.tableSettingsService.saveLocalSettings(formValue);

      this.tableSettingsService.upsertTableSettings(formValue).subscribe({
        next: (response) => {
          console.log('Settings saved successfully', response)
          this.snotif.notify('Table settings updated successfully.')
          this.getSavedSettings(this.settingsForm.get('tableName')?.value)
          // re-save what's in the local settings if needed 
          // (if the saved id for that table is the same as the one that was edited).
          // the function that takes care of saving local settings (this.tableSettingsService.saveLocalSettings) 
          // takes care of updating the signal with the new settings as well. 
          // 1. check the currently savedID for the current table/view being edited.  
          const currentLocalID = localStorage.getItem(`${this.settingsForm.get('tableName')?.value}_current_settings_id`)
          if(currentLocalID == this.settingsForm.get('settingsID')?.value){
            this.tableSettingsService.saveLocalSettings(formValue)
          }
        },
        error: (error) => console.error('Error saving settings', error)
      });
      //this.dialogRef.close();
    } else {
      console.log('Form is invalid');
    }
  }
 
  /**
   * Triggered when the table name changes.
   * – For "ProjectManagement", repopulate the settings FormArray.
   * – Ignores the "actions" field.
   */
  onTableNameChange(selectedTableName: string, rebuild = true): void {
    if (!selectedTableName) {                       // nothing selected
      (this.settingsForm.get('settings') as FormArray).clear();
      this.availableFields = [];
      return;
    }
  
    // 1. ask the back‑end for the list of fields
    this.tableSettingsService
        .getListOfFields({ table_name: selectedTableName })
        .subscribe({
          next: (resp) => {
            const fieldNames: string[] = resp.body ?? [];
  
            // store them for any dropdowns that need them
            this.availableFields = fieldNames;

            if (!rebuild) {             // ←──── EARLY EXIT
              return;
            }
  
            // 2. rebuild the FormArray
            const settingsArray = this.settingsForm.get('settings') as FormArray;
            settingsArray.clear();
  
            fieldNames
              .filter(fn => fn !== 'actions')           // optionally skip
              .forEach(fn => {
                const defaultSetting: TableColumnSetting = {
                  field_name:                 fn,
                  field_name_display:         fn,
                  field_hidden:               'no',
                  field_editable:             'no',
                  field_editable_type:        'text',
                  field_editable_select_values:'',
                  field_trigger_action:       '',
                  field_trigger_action_ref_field:'',
                  field_calc:                 '',
                  field_calc_style:           '',
                  field_calc_ref_field:       '',
                  field_has_filter:           'no',
                  field_is_groupable:         'no',
                  field_is_date:              'no',
                  field_is_date_time:         'no',
                  field_min_width:            null,
                  field_max_width:            null,
                  field_has_tooltip:          'no'
                };
                settingsArray.push(this.createSettingGroup(defaultSetting));
              });
          },
          error: err => {
            console.error('getListOfFields error', err);
            this.snotif.notify('Could not load field list for "' + selectedTableName + '".');
          }
        });
  }

  
}